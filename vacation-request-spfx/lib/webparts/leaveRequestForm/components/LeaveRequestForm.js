var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
import * as React from 'react';
import styles from './LeaveRequestForm.module.scss';
import { escape } from '@microsoft/sp-lodash-subset';
import { PrimaryButton, DefaultButton, TextField, DatePicker, Dropdown, Checkbox, MessageBar, MessageBarType, Spinner, SpinnerSize, Stack, Text, Label } from '@fluentui/react';
import { SharePointService } from '../../../services/SharePointService';
import { LeaveTypeUtils, CommonUtils } from '../../../models';
var LeaveRequestForm = /** @class */ (function (_super) {
    __extends(LeaveRequestForm, _super);
    function LeaveRequestForm(props) {
        var _this = _super.call(this, props) || this;
        _this.onLeaveTypeChange = function (event, option) {
            if (option) {
                var selectedLeaveType = _this.state.leaveTypes.filter(function (lt) { return lt.Id === option.key; })[0] || null;
                _this.setState({
                    formData: __assign(__assign({}, _this.state.formData), { leaveTypeId: option.key }),
                    selectedLeaveType: selectedLeaveType,
                    errors: []
                });
            }
        };
        _this.onStartDateChange = function (date) {
            if (date) {
                var endDate = _this.state.formData.endDate < date ? date : _this.state.formData.endDate;
                _this.setState({
                    formData: __assign(__assign({}, _this.state.formData), { startDate: date, endDate: endDate }),
                    errors: []
                });
            }
        };
        _this.onEndDateChange = function (date) {
            if (date) {
                _this.setState({
                    formData: __assign(__assign({}, _this.state.formData), { endDate: date }),
                    errors: []
                });
            }
        };
        _this.onPartialDayChange = function (event, checked) {
            _this.setState({
                formData: __assign(__assign({}, _this.state.formData), { isPartialDay: !!checked, partialDayHours: checked ? 4 : undefined }),
                errors: []
            });
        };
        _this.onPartialDayHoursChange = function (event, newValue) {
            var hours = newValue ? parseFloat(newValue) : undefined;
            _this.setState({
                formData: __assign(__assign({}, _this.state.formData), { partialDayHours: hours }),
                errors: []
            });
        };
        _this.onCommentsChange = function (event, newValue) {
            _this.setState({
                formData: __assign(__assign({}, _this.state.formData), { comments: newValue || '' }),
                errors: []
            });
        };
        _this.onAttachmentUrlChange = function (event, newValue) {
            _this.setState({
                formData: __assign(__assign({}, _this.state.formData), { attachmentUrl: newValue || '' }),
                errors: []
            });
        };
        _this.onSubmit = function () { return __awaiter(_this, void 0, void 0, function () {
            var validationErrors, request, validation, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        validationErrors = this.validateForm();
                        if (validationErrors.length > 0) {
                            this.setState({ errors: validationErrors });
                            return [2 /*return*/];
                        }
                        this.setState({ isSubmitting: true, errors: [], successMessage: '' });
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 4, , 5]);
                        request = {
                            LeaveTypeId: this.state.formData.leaveTypeId,
                            StartDate: this.state.formData.startDate,
                            EndDate: this.state.formData.endDate,
                            IsPartialDay: this.state.formData.isPartialDay,
                            PartialDayHours: this.state.formData.partialDayHours,
                            RequestComments: this.state.formData.comments,
                            AttachmentURL: this.state.formData.attachmentUrl
                        };
                        return [4 /*yield*/, this.sharePointService.validateLeaveRequest(request)];
                    case 2:
                        validation = _a.sent();
                        if (!validation.isValid) {
                            this.setState({ errors: validation.errors, isSubmitting: false });
                            return [2 /*return*/];
                        }
                        // Create the leave request
                        return [4 /*yield*/, this.sharePointService.createLeaveRequest(request)];
                    case 3:
                        // Create the leave request
                        _a.sent();
                        this.setState({
                            successMessage: 'Leave request submitted successfully!',
                            isSubmitting: false,
                            formData: {
                                leaveTypeId: 0,
                                startDate: new Date(),
                                endDate: new Date(),
                                isPartialDay: false,
                                partialDayHours: undefined,
                                comments: '',
                                attachmentUrl: ''
                            },
                            selectedLeaveType: null
                        });
                        return [3 /*break*/, 5];
                    case 4:
                        error_1 = _a.sent();
                        console.error('Error submitting leave request:', error_1);
                        this.setState({
                            errors: ['Failed to submit leave request. Please try again.'],
                            isSubmitting: false
                        });
                        return [3 /*break*/, 5];
                    case 5: return [2 /*return*/];
                }
            });
        }); };
        _this.onReset = function () {
            _this.setState({
                formData: {
                    leaveTypeId: 0,
                    startDate: new Date(),
                    endDate: new Date(),
                    isPartialDay: false,
                    partialDayHours: undefined,
                    comments: '',
                    attachmentUrl: ''
                },
                selectedLeaveType: null,
                errors: [],
                successMessage: ''
            });
        };
        _this.sharePointService = new SharePointService(props.context);
        _this.state = {
            leaveTypes: [],
            formData: {
                leaveTypeId: 0,
                startDate: new Date(),
                endDate: new Date(),
                isPartialDay: false,
                partialDayHours: undefined,
                comments: '',
                attachmentUrl: ''
            },
            isLoading: true,
            isSubmitting: false,
            errors: [],
            successMessage: '',
            selectedLeaveType: null
        };
        return _this;
    }
    LeaveRequestForm.prototype.componentDidMount = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.loadLeaveTypes()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    LeaveRequestForm.prototype.loadLeaveTypes = function () {
        return __awaiter(this, void 0, void 0, function () {
            var leaveTypes, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        this.setState({ isLoading: true, errors: [] });
                        return [4 /*yield*/, this.sharePointService.getLeaveTypes()];
                    case 1:
                        leaveTypes = _a.sent();
                        this.setState({ leaveTypes: leaveTypes, isLoading: false });
                        return [3 /*break*/, 3];
                    case 2:
                        error_2 = _a.sent();
                        console.error('Error loading leave types:', error_2);
                        this.setState({
                            errors: ['Failed to load leave types. Please refresh the page.'],
                            isLoading: false
                        });
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    LeaveRequestForm.prototype.validateForm = function () {
        var errors = [];
        var _a = this.state, formData = _a.formData, selectedLeaveType = _a.selectedLeaveType;
        if (!formData.leaveTypeId) {
            errors.push('Please select a leave type');
        }
        if (!formData.startDate) {
            errors.push('Please select a start date');
        }
        if (!formData.endDate) {
            errors.push('Please select an end date');
        }
        if (formData.startDate && formData.endDate && formData.endDate < formData.startDate) {
            errors.push('End date must be after start date');
        }
        if (formData.isPartialDay) {
            if (!formData.partialDayHours || formData.partialDayHours <= 0 || formData.partialDayHours > 8) {
                errors.push('Partial day hours must be between 0.5 and 8 hours');
            }
        }
        if ((selectedLeaveType === null || selectedLeaveType === void 0 ? void 0 : selectedLeaveType.RequiresDocumentation) && !formData.attachmentUrl) {
            errors.push("Documentation is required for ".concat(selectedLeaveType.Title));
        }
        return errors;
    };
    LeaveRequestForm.prototype.render = function () {
        var _a;
        var _b = this.props, hasTeamsContext = _b.hasTeamsContext, userDisplayName = _b.userDisplayName;
        var _c = this.state, leaveTypes = _c.leaveTypes, formData = _c.formData, isLoading = _c.isLoading, isSubmitting = _c.isSubmitting, errors = _c.errors, successMessage = _c.successMessage, selectedLeaveType = _c.selectedLeaveType;
        if (isLoading) {
            return (React.createElement("section", { className: "".concat(styles.leaveRequestForm, " ").concat(hasTeamsContext ? styles.teams : '') },
                React.createElement(Stack, { horizontalAlign: "center", tokens: { padding: 20 } },
                    React.createElement(Spinner, { size: SpinnerSize.large, label: "Loading leave types..." }))));
        }
        var leaveTypeOptions = LeaveTypeUtils.toDropdownOptions(leaveTypes);
        var totalDays = formData.startDate && formData.endDate ?
            CommonUtils.calculateBusinessDays(formData.startDate, formData.endDate) : 0;
        return (React.createElement("section", { className: "".concat(styles.leaveRequestForm, " ").concat(hasTeamsContext ? styles.teams : '') },
            React.createElement(Stack, { tokens: { childrenGap: 20 } },
                React.createElement(Stack.Item, null,
                    React.createElement(Text, { variant: "xxLarge", as: "h1" }, "Leave Request Form"),
                    React.createElement(Text, { variant: "medium" },
                        "Welcome, ",
                        escape(userDisplayName),
                        "! Submit your leave request below.")),
                errors.length > 0 && (React.createElement(MessageBar, { messageBarType: MessageBarType.error, isMultiline: true },
                    React.createElement("ul", { style: { margin: 0, paddingLeft: 20 } }, errors.map(function (error, index) { return (React.createElement("li", { key: index }, error)); })))),
                successMessage && (React.createElement(MessageBar, { messageBarType: MessageBarType.success }, successMessage)),
                React.createElement(Stack, { tokens: { childrenGap: 15 } },
                    React.createElement(Dropdown, { label: "Leave Type *", placeholder: "Select a leave type", options: leaveTypeOptions, selectedKey: formData.leaveTypeId || undefined, onChange: this.onLeaveTypeChange, disabled: isSubmitting, required: true }),
                    selectedLeaveType && (React.createElement(Stack.Item, null,
                        React.createElement(Text, { variant: "small", styles: { root: { color: '#666' } } },
                            selectedLeaveType.Description,
                            selectedLeaveType.MaxDaysPerRequest &&
                                " (Max: ".concat(selectedLeaveType.MaxDaysPerRequest, " days per request)"),
                            selectedLeaveType.RequiresDocumentation && ' - Documentation required'))),
                    React.createElement(Stack, { horizontal: true, tokens: { childrenGap: 15 } },
                        React.createElement(Stack.Item, { grow: true },
                            React.createElement(DatePicker, { label: "Start Date *", value: formData.startDate, onSelectDate: this.onStartDateChange, disabled: isSubmitting, isRequired: true, minDate: new Date() })),
                        React.createElement(Stack.Item, { grow: true },
                            React.createElement(DatePicker, { label: "End Date *", value: formData.endDate, onSelectDate: this.onEndDateChange, disabled: isSubmitting, isRequired: true, minDate: formData.startDate }))),
                    totalDays > 0 && (React.createElement(Stack.Item, null,
                        React.createElement(Label, null,
                            "Total Business Days: ",
                            totalDays))),
                    React.createElement(Checkbox, { label: "Partial Day Request", checked: formData.isPartialDay, onChange: this.onPartialDayChange, disabled: isSubmitting }),
                    formData.isPartialDay && (React.createElement(TextField, { label: "Hours *", type: "number", value: ((_a = formData.partialDayHours) === null || _a === void 0 ? void 0 : _a.toString()) || '', onChange: this.onPartialDayHoursChange, disabled: isSubmitting, suffix: "hours", min: 0.5, max: 8, step: 0.5, required: true })),
                    React.createElement(TextField, { label: "Comments", multiline: true, rows: 3, value: formData.comments, onChange: this.onCommentsChange, disabled: isSubmitting, placeholder: "Optional comments about your leave request" }),
                    (selectedLeaveType === null || selectedLeaveType === void 0 ? void 0 : selectedLeaveType.RequiresDocumentation) && (React.createElement(TextField, { label: "Documentation URL *", value: formData.attachmentUrl, onChange: this.onAttachmentUrlChange, disabled: isSubmitting, placeholder: "URL to supporting documentation", required: true })),
                    React.createElement(Stack, { horizontal: true, tokens: { childrenGap: 10 } },
                        React.createElement(PrimaryButton, { text: isSubmitting ? "Submitting..." : "Submit Request", onClick: this.onSubmit, disabled: isSubmitting, iconProps: isSubmitting ? { iconName: 'Sync' } : { iconName: 'Send' } }),
                        React.createElement(DefaultButton, { text: "Reset Form", onClick: this.onReset, disabled: isSubmitting, iconProps: { iconName: 'Clear' } }))))));
    };
    return LeaveRequestForm;
}(React.Component));
export default LeaveRequestForm;
//# sourceMappingURL=LeaveRequestForm.js.map