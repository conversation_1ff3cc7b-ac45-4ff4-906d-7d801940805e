.leaveRequestForm_f12f0c02{color:"[theme:bodyText, default: #323130]";color:var(--bodyText);padding:20px;max-width:800px;margin:0 auto}.leaveRequestForm_f12f0c02.teams_f12f0c02{font-family:"Segoe UI",-apple-system,BlinkMacSystemFont,Roboto,"Helvetica Neue",sans-serif}.leaveRequestForm_f12f0c02 .formSection_f12f0c02{background:"[theme:bodyBackground, default: #ffffff]";background:var(--bodyBackground);border:1px solid "[theme:neutralLight, default: #edebe9]";border:1px solid var(--neutralLight);border-radius:4px;padding:24px;margin-bottom:16px;box-shadow:0 1px 3px rgba(0,0,0,.1)}.leaveRequestForm_f12f0c02 .sectionTitle_f12f0c02{font-size:18px;font-weight:600;margin-bottom:16px;color:"[theme:themePrimary, default: #0078d4]";color:var(--themePrimary)}.leaveRequestForm_f12f0c02 .fieldGroup_f12f0c02{margin-bottom:16px}.leaveRequestForm_f12f0c02 .inlineFields_f12f0c02{display:-ms-flexbox;display:flex;gap:16px}.leaveRequestForm_f12f0c02 .inlineFields_f12f0c02>*{-ms-flex:1;flex:1}.leaveRequestForm_f12f0c02 .helpText_f12f0c02{font-size:12px;color:"[theme:neutralSecondary, default: #605e5c]";color:var(--neutralSecondary);margin-top:4px;font-style:italic}.leaveRequestForm_f12f0c02 .summaryBox_f12f0c02{background:"[theme:neutralLighterAlt, default: #faf9f8]";background:var(--neutralLighterAlt);border:1px solid "[theme:neutralLight, default: #edebe9]";border:1px solid var(--neutralLight);border-radius:4px;padding:16px;margin:16px 0}.leaveRequestForm_f12f0c02 .buttonGroup_f12f0c02{display:-ms-flexbox;display:flex;gap:12px;margin-top:24px;-ms-flex-pack:start;justify-content:flex-start}.leaveRequestForm_f12f0c02 .loadingContainer_f12f0c02{display:-ms-flexbox;display:flex;-ms-flex-pack:center;justify-content:center;-ms-flex-align:center;align-items:center;min-height:200px}.leaveRequestForm_f12f0c02 .errorList_f12f0c02{margin:0;padding-left:20px}.leaveRequestForm_f12f0c02 .errorList_f12f0c02 li{margin-bottom:4px}.leaveRequestForm_f12f0c02 .leaveTypeInfo_f12f0c02{background:"[theme:themeLight, default: #deecf9]";background:var(--themeLight);border-left:4px solid "[theme:themePrimary, default: #0078d4]";border-left:4px solid var(--themePrimary);padding:12px 16px;margin:8px 0;border-radius:0 4px 4px 0}.leaveRequestForm_f12f0c02 .daysSummary_f12f0c02{font-weight:600;color:"[theme:themePrimary, default: #0078d4]";color:var(--themePrimary);font-size:14px}.leaveRequestForm_f12f0c02 .requiredField_f12f0c02::after{content:" *";color:"[theme:errorText, default: #a80000]";color:var(--errorText)}
/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImZpbGU6Ly8vVXNlcnMvcXRzL1JlcG9zL3ZhY2F0aW9uLXJlcXVlc3Qtc3BmeC92YWNhdGlvbi1yZXF1ZXN0LXNwZngvc3JjL3dlYnBhcnRzL2xlYXZlUmVxdWVzdEZvcm0vY29tcG9uZW50cy9MZWF2ZVJlcXVlc3RGb3JtLm1vZHVsZS5zY3NzIiwiJHN0ZGluIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVBLDJCQUNFLE1BQUEscUNBQ0EsTUFBQSxnQkFDQSxRQUFBLEtBQ0EsVUFBQSxNQUNBLE9BQUEsRUFBQSxLQUVBLDBDQUNFLFlBQUEsVUNDcUIsQ0REckIsYUNDb0MsQ0REcEMsa0JDQ3dELENERHhELE1DQ2tFLENERGxFLGdCQ0NvRixDRERwRixXQUdGLGlEQUNFLFdBQUEsMkNBQ0EsV0FBQSxzQkFDQSxPQUFBLElBQUEsTUFBQSx5Q0FDQSxPQUFBLElBQUEsTUFBQSxvQkFDQSxjQUFBLElBQ0EsUUFBQSxLQUNBLGNBQUEsS0FDQSxXQUFBLEVBQUEsSUFBQSxJQUFBLGVBR0Ysa0RBQ0UsVUFBQSxLQUNBLFlBQUEsSUFDQSxjQUFBLEtBQ0EsTUFBQSx5Q0FDQSxNQUFBLG9CQUdGLGdEQUNFLGNBQUEsS0FHRixrREFDRSxRQUFBLFlBQUEsUUFBQSxLQUNBLElBQUEsS0FFQSxvREFDRSxTQUFBLEVBQUEsS0FBQSxFQUlKLDhDQUNFLFVBQUEsS0FDQSxNQUFBLDZDQUNBLE1BQUEsd0JBQ0EsV0FBQSxJQUNBLFdBQUEsT0FHRixnREFDRSxXQUFBLDhDQUNBLFdBQUEseUJBQ0EsT0FBQSxJQUFBLE1BQUEseUNBQ0EsT0FBQSxJQUFBLE1BQUEsb0JBQ0EsY0FBQSxJQUNBLFFBQUEsS0FDQSxPQUFBLEtBQUEsRUFHRixpREFDRSxRQUFBLFlBQUEsUUFBQSxLQUNBLElBQUEsS0FDQSxXQUFBLEtBQ0EsY0FBQSxNQUFBLGdCQUFBLFdBR0Ysc0RBQ0UsUUFBQSxZQUFBLFFBQUEsS0FDQSxjQUFBLE9BQUEsZ0JBQUEsT0FDQSxlQUFBLE9BQUEsWUFBQSxPQUNBLFdBQUEsTUFHRiwrQ0FDRSxPQUFBLEVBQ0EsYUFBQSxLQUVBLGtEQUNFLGNBQUEsSUFJSixtREFDRSxXQUFBLHVDQUNBLFdBQUEsa0JBQ0EsWUFBQSxJQUFBLE1BQUEseUNBQ0EsWUFBQSxJQUFBLE1BQUEsb0JBQ0EsUUFBQSxLQUFBLEtBQ0EsT0FBQSxJQUFBLEVBQ0EsY0FBQSxFQUFBLElBQUEsSUFBQSxFQUdGLGlEQUNFLFlBQUEsSUFDQSxNQUFBLHlDQUNBLE1BQUEsb0JBQ0EsVUFBQSxLQUdGLDBEQUNFLFFBQUEsS0FDQSxNQUFBLHNDQUNBLE1BQUEifQ== */