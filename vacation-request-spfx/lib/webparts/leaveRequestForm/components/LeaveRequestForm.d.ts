import * as React from 'react';
import type { ILeaveRequestFormProps } from './ILeaveRequestFormProps';
import { ILeaveType, ILeaveRequestFormData } from '../../../models';
interface ILeaveRequestFormState {
    leaveTypes: ILeaveType[];
    formData: ILeaveRequestFormData;
    isLoading: boolean;
    isSubmitting: boolean;
    errors: string[];
    successMessage: string;
    selectedLeaveType: ILeaveType | null;
}
export default class LeaveRequestForm extends React.Component<ILeaveRequestFormProps, ILeaveRequestFormState> {
    private sharePointService;
    constructor(props: ILeaveRequestFormProps);
    componentDidMount(): Promise<void>;
    private loadLeaveTypes;
    private onLeaveTypeChange;
    private onStartDateChange;
    private onEndDateChange;
    private onPartialDayChange;
    private onPartialDayHoursChange;
    private onCommentsChange;
    private onAttachmentUrlChange;
    private validateForm;
    private onSubmit;
    private onReset;
    render(): React.ReactElement<ILeaveRequestFormProps>;
}
export {};
//# sourceMappingURL=LeaveRequestForm.d.ts.map