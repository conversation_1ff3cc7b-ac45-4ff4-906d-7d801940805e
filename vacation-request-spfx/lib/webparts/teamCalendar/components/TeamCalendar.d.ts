import * as React from 'react';
import type { ITeamCalendarProps } from './ITeamCalendarProps';
import { ILeaveRequest, ILeaveType } from '../../../models';
interface ICalendarEvent {
    id: string;
    title: string;
    start: string;
    end: string;
    allDay: boolean;
    backgroundColor: string;
    borderColor: string;
    textColor: string;
    extendedProps: {
        leaveRequest: ILeaveRequest;
        leaveType: ILeaveType;
    };
}
interface ITeamCalendarState {
    events: ICalendarEvent[];
    leaveTypes: ILeaveType[];
    isLoading: boolean;
    error: string;
    currentView: string;
    selectedEvent: ICalendarEvent | undefined;
    isPanelOpen: boolean;
    filters: {
        leaveTypeId: number | undefined;
        department: string;
        showOnlyApproved: boolean;
    };
}
export default class TeamCalendar extends React.Component<ITeamCalendarProps, ITeamCalendarState> {
    private sharePointService;
    private calendarRef;
    constructor(props: ITeamCalendarProps);
    componentDidMount(): Promise<void>;
    private loadData;
    private convertToCalendarEvents;
    private shouldShowRequest;
    private getEndDateForCalendar;
    private getTextColor;
    private onEventClick;
    private onViewChange;
    private onFilterChange;
    private onClosePanel;
    private onRefresh;
    private onExportCalendar;
    private convertEventsToCSV;
    private downloadCSV;
    render(): React.ReactElement<ITeamCalendarProps>;
}
export {};
//# sourceMappingURL=TeamCalendar.d.ts.map