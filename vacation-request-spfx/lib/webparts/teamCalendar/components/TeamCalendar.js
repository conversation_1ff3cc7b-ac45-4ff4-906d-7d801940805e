var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
import * as React from 'react';
import styles from './TeamCalendar.module.scss';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import { Stack, Text, Dropdown, DefaultButton, MessageBar, MessageBarType, Spinner, SpinnerSize, Panel, PanelType, TextField, Toggle } from '@fluentui/react';
import { SharePointService } from '../../../services';
import { LeaveTypeUtils, CommonUtils } from '../../../models';
var TeamCalendar = /** @class */ (function (_super) {
    __extends(TeamCalendar, _super);
    function TeamCalendar(props) {
        var _this = _super.call(this, props) || this;
        _this.calendarRef = React.createRef();
        _this.onEventClick = function (info) {
            var event = info.event;
            var calendarEvent = {
                id: event.id,
                title: event.title,
                start: event.startStr,
                end: event.endStr,
                allDay: event.allDay,
                backgroundColor: event.backgroundColor,
                borderColor: event.borderColor,
                textColor: event.textColor,
                extendedProps: event.extendedProps
            };
            _this.setState({
                selectedEvent: calendarEvent,
                isPanelOpen: true
            });
        };
        _this.onViewChange = function (event, option) {
            if (option && _this.calendarRef.current) {
                var calendarApi = _this.calendarRef.current.getApi();
                calendarApi.changeView(option.key);
                _this.setState({ currentView: option.key });
            }
        };
        _this.onFilterChange = function (filterType, value) { return __awaiter(_this, void 0, void 0, function () {
            var newFilters;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        newFilters = __assign(__assign({}, this.state.filters), (_a = {}, _a[filterType] = value, _a));
                        this.setState({ filters: newFilters });
                        // Reload events with new filters
                        return [4 /*yield*/, this.loadData()];
                    case 1:
                        // Reload events with new filters
                        _b.sent();
                        return [2 /*return*/];
                }
            });
        }); };
        _this.onClosePanel = function () {
            _this.setState({
                isPanelOpen: false,
                selectedEvent: undefined
            });
        };
        _this.onRefresh = function () { return __awaiter(_this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.loadData()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); };
        _this.onExportCalendar = function () {
            // Implementation for exporting calendar data
            var events = _this.state.events;
            var csvContent = _this.convertEventsToCSV(events);
            _this.downloadCSV(csvContent, 'team-calendar.csv');
        };
        _this.sharePointService = new SharePointService(props.context);
        _this.state = {
            events: [],
            leaveTypes: [],
            isLoading: true,
            error: '',
            currentView: 'dayGridMonth',
            selectedEvent: undefined,
            isPanelOpen: false,
            filters: {
                leaveTypeId: undefined,
                department: '',
                showOnlyApproved: true
            }
        };
        return _this;
    }
    TeamCalendar.prototype.componentDidMount = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.loadData()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    TeamCalendar.prototype.loadData = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a, leaveTypes, leaveRequests, events, error_1;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 2, , 3]);
                        this.setState({ isLoading: true, error: '' });
                        return [4 /*yield*/, Promise.all([
                                this.sharePointService.getLeaveTypes(),
                                this.sharePointService.getAllLeaveRequests()
                            ])];
                    case 1:
                        _a = _b.sent(), leaveTypes = _a[0], leaveRequests = _a[1];
                        events = this.convertToCalendarEvents(leaveRequests, leaveTypes);
                        this.setState({
                            leaveTypes: leaveTypes,
                            events: events,
                            isLoading: false
                        });
                        return [3 /*break*/, 3];
                    case 2:
                        error_1 = _b.sent();
                        console.error('Error loading calendar data:', error_1);
                        this.setState({
                            error: 'Failed to load calendar data. Please refresh the page.',
                            isLoading: false
                        });
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    TeamCalendar.prototype.convertToCalendarEvents = function (leaveRequests, leaveTypes) {
        var _this = this;
        return leaveRequests
            .filter(function (request) { return _this.shouldShowRequest(request); })
            .map(function (request) {
            var leaveType = leaveTypes.filter(function (lt) { return lt.Id === request.LeaveType.Id; })[0];
            var color = (leaveType === null || leaveType === void 0 ? void 0 : leaveType.ColorCode) || '#0078d4';
            return {
                id: request.Id.toString(),
                title: "".concat(request.Requester.Title, " - ").concat(request.LeaveType.Title),
                start: request.StartDate.toISOString().split('T')[0],
                end: _this.getEndDateForCalendar(request.EndDate),
                allDay: !request.IsPartialDay,
                backgroundColor: color,
                borderColor: color,
                textColor: _this.getTextColor(color),
                extendedProps: {
                    leaveRequest: request,
                    leaveType: leaveType || { Id: 0, Title: 'Unknown', IsActive: true, RequiresApproval: true, RequiresDocumentation: false, Created: new Date(), Modified: new Date() }
                }
            };
        });
    };
    TeamCalendar.prototype.shouldShowRequest = function (request) {
        var filters = this.state.filters;
        // Filter by approval status
        if (filters.showOnlyApproved && request.ApprovalStatus !== 'Approved') {
            return false;
        }
        // Filter by leave type
        if (filters.leaveTypeId && request.LeaveType.Id !== filters.leaveTypeId) {
            return false;
        }
        // Filter by department
        if (filters.department && request.Department !== filters.department) {
            return false;
        }
        return true;
    };
    TeamCalendar.prototype.getEndDateForCalendar = function (endDate) {
        // FullCalendar expects end date to be the day after for all-day events
        var nextDay = new Date(endDate.getTime());
        nextDay.setDate(nextDay.getDate() + 1);
        return nextDay.toISOString().split('T')[0];
    };
    TeamCalendar.prototype.getTextColor = function (backgroundColor) {
        // Simple contrast calculation
        var hex = backgroundColor.replace('#', '');
        var r = parseInt(hex.substr(0, 2), 16);
        var g = parseInt(hex.substr(2, 2), 16);
        var b = parseInt(hex.substr(4, 2), 16);
        var brightness = (r * 299 + g * 587 + b * 114) / 1000;
        return brightness > 128 ? '#000000' : '#ffffff';
    };
    TeamCalendar.prototype.convertEventsToCSV = function (events) {
        var headers = ['Employee', 'Leave Type', 'Start Date', 'End Date', 'Status', 'Comments'];
        var rows = events.map(function (event) { return [
            event.extendedProps.leaveRequest.Requester.Title,
            event.extendedProps.leaveRequest.LeaveType.Title,
            event.extendedProps.leaveRequest.StartDate.toLocaleDateString(),
            event.extendedProps.leaveRequest.EndDate.toLocaleDateString(),
            event.extendedProps.leaveRequest.ApprovalStatus,
            event.extendedProps.leaveRequest.RequestComments || ''
        ]; });
        return __spreadArray([headers], rows, true).map(function (row) { return row.join(','); }).join('\n');
    };
    TeamCalendar.prototype.downloadCSV = function (content, filename) {
        var blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
        var link = document.createElement('a');
        var url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };
    TeamCalendar.prototype.render = function () {
        var _this = this;
        var hasTeamsContext = this.props.hasTeamsContext;
        var _a = this.state, events = _a.events, leaveTypes = _a.leaveTypes, isLoading = _a.isLoading, error = _a.error, currentView = _a.currentView, selectedEvent = _a.selectedEvent, isPanelOpen = _a.isPanelOpen, filters = _a.filters;
        if (isLoading) {
            return (React.createElement("section", { className: "".concat(styles.teamCalendar, " ").concat(hasTeamsContext ? styles.teams : '') },
                React.createElement(Stack, { horizontalAlign: "center", tokens: { padding: 20 } },
                    React.createElement(Spinner, { size: SpinnerSize.large, label: "Loading team calendar..." }))));
        }
        var viewOptions = [
            { key: 'dayGridMonth', text: 'Month View' },
            { key: 'timeGridWeek', text: 'Week View' },
            { key: 'timeGridDay', text: 'Day View' },
            { key: 'listWeek', text: 'List View' }
        ];
        var leaveTypeOptions = __spreadArray([
            { key: 'all', text: 'All Leave Types' }
        ], LeaveTypeUtils.toDropdownOptions(leaveTypes), true);
        return (React.createElement("section", { className: "".concat(styles.teamCalendar, " ").concat(hasTeamsContext ? styles.teams : '') },
            React.createElement(Stack, { tokens: { childrenGap: 20 } },
                React.createElement(Stack.Item, null,
                    React.createElement(Text, { variant: "xxLarge", as: "h1" }, "Team Leave Calendar"),
                    React.createElement(Text, { variant: "medium" }, "View team leave requests and plan coverage")),
                error && (React.createElement(MessageBar, { messageBarType: MessageBarType.error }, error)),
                React.createElement(Stack, { horizontal: true, tokens: { childrenGap: 15 }, wrap: true },
                    React.createElement(Dropdown, { label: "View", options: viewOptions, selectedKey: currentView, onChange: this.onViewChange, styles: { root: { minWidth: 120 } } }),
                    React.createElement(Dropdown, { label: "Leave Type", options: leaveTypeOptions, selectedKey: filters.leaveTypeId || 'all', onChange: function (e, option) { return _this.onFilterChange('leaveTypeId', (option === null || option === void 0 ? void 0 : option.key) === 'all' ? undefined : option === null || option === void 0 ? void 0 : option.key); }, styles: { root: { minWidth: 150 } } }),
                    React.createElement(TextField, { label: "Department", value: filters.department, onChange: function (e, value) { return _this.onFilterChange('department', value || ''); }, styles: { root: { minWidth: 120 } } }),
                    React.createElement(Toggle, { label: "Show only approved", checked: filters.showOnlyApproved, onChange: function (e, checked) { return _this.onFilterChange('showOnlyApproved', !!checked); } }),
                    React.createElement(Stack, { horizontal: true, tokens: { childrenGap: 10 } },
                        React.createElement(DefaultButton, { text: "Refresh", iconProps: { iconName: 'Refresh' }, onClick: this.onRefresh }),
                        React.createElement(DefaultButton, { text: "Export", iconProps: { iconName: 'Download' }, onClick: this.onExportCalendar }))),
                React.createElement(Stack.Item, { className: styles.calendarContainer },
                    React.createElement(FullCalendar, { ref: this.calendarRef, plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin], initialView: currentView, headerToolbar: {
                            left: 'prev,next today',
                            center: 'title',
                            right: ''
                        }, events: events, eventClick: this.onEventClick, height: "auto", eventDisplay: "block", dayMaxEvents: 3, moreLinkClick: "popover", eventTimeFormat: {
                            hour: 'numeric',
                            minute: '2-digit',
                            meridiem: 'short'
                        }, slotLabelFormat: {
                            hour: 'numeric',
                            minute: '2-digit',
                            meridiem: 'short'
                        } }))),
            React.createElement(Panel, { isOpen: isPanelOpen, onDismiss: this.onClosePanel, type: PanelType.medium, headerText: "Leave Request Details", closeButtonAriaLabel: "Close" }, selectedEvent && (React.createElement(Stack, { tokens: { childrenGap: 15 } },
                React.createElement(Stack.Item, null,
                    React.createElement(Text, { variant: "large", as: "h3" }, selectedEvent.extendedProps.leaveRequest.Requester.Title),
                    React.createElement(Text, { variant: "medium" }, selectedEvent.extendedProps.leaveRequest.LeaveType.Title)),
                React.createElement(Stack, { tokens: { childrenGap: 10 } },
                    React.createElement(Stack, { horizontal: true, tokens: { childrenGap: 20 } },
                        React.createElement(Stack.Item, null,
                            React.createElement(Text, { variant: "smallPlus", styles: { root: { fontWeight: 600 } } }, "Start Date:"),
                            React.createElement(Text, { variant: "small" }, selectedEvent.extendedProps.leaveRequest.StartDate.toLocaleDateString())),
                        React.createElement(Stack.Item, null,
                            React.createElement(Text, { variant: "smallPlus", styles: { root: { fontWeight: 600 } } }, "End Date:"),
                            React.createElement(Text, { variant: "small" }, selectedEvent.extendedProps.leaveRequest.EndDate.toLocaleDateString()))),
                    React.createElement(Stack.Item, null,
                        React.createElement(Text, { variant: "smallPlus", styles: { root: { fontWeight: 600 } } }, "Status:"),
                        React.createElement(Text, { variant: "small" }, selectedEvent.extendedProps.leaveRequest.ApprovalStatus)),
                    React.createElement(Stack.Item, null,
                        React.createElement(Text, { variant: "smallPlus", styles: { root: { fontWeight: 600 } } }, "Total Days:"),
                        React.createElement(Text, { variant: "small" }, selectedEvent.extendedProps.leaveRequest.TotalDays ||
                            CommonUtils.calculateBusinessDays(selectedEvent.extendedProps.leaveRequest.StartDate, selectedEvent.extendedProps.leaveRequest.EndDate))),
                    selectedEvent.extendedProps.leaveRequest.IsPartialDay && (React.createElement(Stack.Item, null,
                        React.createElement(Text, { variant: "smallPlus", styles: { root: { fontWeight: 600 } } }, "Partial Day Hours:"),
                        React.createElement(Text, { variant: "small" }, selectedEvent.extendedProps.leaveRequest.PartialDayHours))),
                    selectedEvent.extendedProps.leaveRequest.RequestComments && (React.createElement(Stack.Item, null,
                        React.createElement(Text, { variant: "smallPlus", styles: { root: { fontWeight: 600 } } }, "Comments:"),
                        React.createElement(Text, { variant: "small" }, selectedEvent.extendedProps.leaveRequest.RequestComments))),
                    React.createElement(Stack.Item, null,
                        React.createElement(Text, { variant: "smallPlus", styles: { root: { fontWeight: 600 } } }, "Submitted:"),
                        React.createElement(Text, { variant: "small" }, selectedEvent.extendedProps.leaveRequest.SubmissionDate.toLocaleDateString())),
                    selectedEvent.extendedProps.leaveRequest.Department && (React.createElement(Stack.Item, null,
                        React.createElement(Text, { variant: "smallPlus", styles: { root: { fontWeight: 600 } } }, "Department:"),
                        React.createElement(Text, { variant: "small" }, selectedEvent.extendedProps.leaveRequest.Department)))))))));
    };
    return TeamCalendar;
}(React.Component));
export default TeamCalendar;
//# sourceMappingURL=TeamCalendar.js.map