require("./TeamCalendar.module.css");
var styles = {
    teamCalendar: 'teamCalendar_3b8e0513',
    teams: 'teams_3b8e0513',
    calendarContainer: 'calendarContainer_3b8e0513',
    filterSection: 'filterSection_3b8e0513',
    toolbarSection: 'toolbarSection_3b8e0513',
    eventDetails: 'eventDetails_3b8e0513',
    detailRow: 'detailRow_3b8e0513',
    label: 'label_3b8e0513',
    value: 'value_3b8e0513',
    statusBadge: 'statusBadge_3b8e0513',
    pending: 'pending_3b8e0513',
    approved: 'approved_3b8e0513',
    rejected: 'rejected_3b8e0513',
    loadingContainer: 'loadingContainer_3b8e0513'
};
export default styles;
//# sourceMappingURL=TeamCalendar.module.scss.js.map