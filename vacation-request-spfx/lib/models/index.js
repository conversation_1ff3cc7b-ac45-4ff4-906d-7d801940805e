/**
 * Export all model interfaces and utilities
 */
// Leave Request models
export * from './ILeaveRequest';
// Leave Type models
export * from './ILeaveType';
// Leave Balance models
export * from './ILeaveBalance';
/**
 * Common constants
 */
export var LIST_NAMES = {
    LEAVE_REQUESTS: 'LeaveRequests',
    LEAVE_TYPES: 'LeaveTypes',
    LEAVE_BALANCES: 'LeaveBalances'
};
export var FIELD_NAMES = {
    LEAVE_REQUESTS: {
        REQUESTER: 'Requester',
        EMPLOYEE_ID: 'EmployeeID',
        DEPARTMENT: 'Department',
        MANAGER: 'Manager',
        LEAVE_TYPE: 'LeaveType',
        START_DATE: 'StartDate',
        END_DATE: 'EndDate',
        TOTAL_DAYS: 'TotalDays',
        IS_PARTIAL_DAY: 'IsPartialDay',
        PARTIAL_DAY_HOURS: 'PartialDayHours',
        REQUEST_COMMENTS: 'RequestComments',
        APPROVAL_STATUS: 'ApprovalStatus',
        APPROVAL_DATE: 'ApprovalDate',
        APPROVAL_COMMENTS: 'ApprovalComments',
        SUBMISSION_DATE: 'SubmissionDate',
        ATTACHMENT_URL: 'AttachmentURL',
        WORKFLOW_INSTANCE_ID: 'WorkflowInstanceID',
        NOTIFICATIONS_SENT: 'NotificationsSent',
        CALENDAR_EVENT_ID: 'CalendarEventID'
    },
    LEAVE_TYPES: {
        TITLE: 'Title',
        DESCRIPTION: 'Description',
        IS_ACTIVE: 'IsActive',
        REQUIRES_APPROVAL: 'RequiresApproval',
        MAX_DAYS_PER_REQUEST: 'MaxDaysPerRequest',
        REQUIRES_DOCUMENTATION: 'RequiresDocumentation',
        COLOR_CODE: 'ColorCode',
        POLICY_URL: 'PolicyURL'
    },
    LEAVE_BALANCES: {
        EMPLOYEE: 'Employee',
        LEAVE_TYPE: 'LeaveType',
        TOTAL_ALLOWANCE: 'TotalAllowance',
        USED_DAYS: 'UsedDays',
        REMAINING_DAYS: 'RemainingDays',
        CARRY_OVER_DAYS: 'CarryOverDays',
        EFFECTIVE_DATE: 'EffectiveDate',
        EXPIRATION_DATE: 'ExpirationDate'
    }
};
/**
 * Utility functions for common operations
 */
var CommonUtils = /** @class */ (function () {
    function CommonUtils() {
    }
    /**
     * Format date for SharePoint
     */
    CommonUtils.formatDateForSharePoint = function (date) {
        return date.toISOString();
    };
    /**
     * Parse SharePoint date
     */
    CommonUtils.parseSharePointDate = function (dateString) {
        return new Date(dateString);
    };
    /**
     * Generate title for leave request
     */
    CommonUtils.generateLeaveRequestTitle = function (employeeName, leaveTypeName, startDate) {
        var formattedDate = startDate.toLocaleDateString();
        return "".concat(employeeName, " - ").concat(leaveTypeName, " - ").concat(formattedDate);
    };
    /**
     * Calculate business days between two dates
     */
    CommonUtils.calculateBusinessDays = function (startDate, endDate) {
        var count = 0;
        var current = new Date(startDate);
        while (current <= endDate) {
            var dayOfWeek = current.getDay();
            if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday (0) or Saturday (6)
                count++;
            }
            current.setDate(current.getDate() + 1);
        }
        return count;
    };
    /**
     * Validate email format
     */
    CommonUtils.isValidEmail = function (email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };
    /**
     * Truncate text to specified length
     */
    CommonUtils.truncateText = function (text, maxLength) {
        if (text.length <= maxLength)
            return text;
        return text.substring(0, maxLength - 3) + '...';
    };
    /**
     * Deep clone object
     */
    CommonUtils.deepClone = function (obj) {
        return JSON.parse(JSON.stringify(obj));
    };
    /**
     * Check if date is weekend
     */
    CommonUtils.isWeekend = function (date) {
        var dayOfWeek = date.getDay();
        return dayOfWeek === 0 || dayOfWeek === 6; // Sunday or Saturday
    };
    /**
     * Get next business day
     */
    CommonUtils.getNextBusinessDay = function (date) {
        var nextDay = new Date(date);
        nextDay.setDate(nextDay.getDate() + 1);
        while (this.isWeekend(nextDay)) {
            nextDay.setDate(nextDay.getDate() + 1);
        }
        return nextDay;
    };
    return CommonUtils;
}());
export { CommonUtils };
//# sourceMappingURL=index.js.map