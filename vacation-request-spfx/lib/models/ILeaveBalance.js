/**
 * Utility functions for Leave Balances
 */
var LeaveBalanceUtils = /** @class */ (function () {
    function LeaveBalanceUtils() {
    }
    /**
     * Calculate remaining days
     */
    LeaveBalanceUtils.calculateRemainingDays = function (totalAllowance, usedDays, carryOverDays) {
        if (carryOverDays === void 0) { carryOverDays = 0; }
        return Math.max(0, totalAllowance + carryOverDays - usedDays);
    };
    /**
     * Check if balance is expiring soon (within 30 days)
     */
    LeaveBalanceUtils.isExpiringSoon = function (expirationDate, daysThreshold) {
        if (daysThreshold === void 0) { daysThreshold = 30; }
        var today = new Date();
        var diffTime = expirationDate.getTime() - today.getTime();
        var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= daysThreshold && diffDays >= 0;
    };
    /**
     * Calculate days until expiry
     */
    LeaveBalanceUtils.getDaysUntilExpiry = function (expirationDate) {
        var today = new Date();
        var diffTime = expirationDate.getTime() - today.getTime();
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    };
    /**
     * Calculate usage percentage
     */
    LeaveBalanceUtils.calculateUsagePercentage = function (usedDays, totalAllowance, carryOverDays) {
        if (carryOverDays === void 0) { carryOverDays = 0; }
        var totalAvailable = totalAllowance + carryOverDays;
        return totalAvailable > 0 ? Math.round((usedDays / totalAvailable) * 100) : 0;
    };
    /**
     * Group balances by employee
     */
    LeaveBalanceUtils.groupByEmployee = function (balances) {
        var grouped = new Map();
        balances.forEach(function (balance) {
            var employeeId = balance.Employee.Id;
            if (!grouped.has(employeeId)) {
                grouped.set(employeeId, []);
            }
            grouped.get(employeeId).push(balance);
        });
        return grouped;
    };
    /**
     * Create Leave Balance summary for an employee
     */
    LeaveBalanceUtils.createEmployeeSummary = function (employeeBalances) {
        var _this = this;
        if (employeeBalances.length === 0)
            return null;
        var employee = employeeBalances[0].Employee;
        var balances = employeeBalances.map(function (balance) { return ({
            leaveTypeId: balance.LeaveType.Id,
            leaveTypeName: balance.LeaveType.Title,
            totalAllowance: balance.TotalAllowance,
            usedDays: balance.UsedDays,
            remainingDays: balance.RemainingDays,
            carryOverDays: balance.CarryOverDays,
            effectiveDate: balance.EffectiveDate,
            expirationDate: balance.ExpirationDate,
            isExpiringSoon: _this.isExpiringSoon(balance.ExpirationDate),
            daysUntilExpiry: _this.getDaysUntilExpiry(balance.ExpirationDate)
        }); });
        return {
            employeeId: employee.Id,
            employeeName: employee.Title,
            balances: balances,
            totalAllowance: balances.reduce(function (sum, b) { return sum + b.totalAllowance; }, 0),
            totalUsed: balances.reduce(function (sum, b) { return sum + b.usedDays; }, 0),
            totalRemaining: balances.reduce(function (sum, b) { return sum + b.remainingDays; }, 0)
        };
    };
    /**
     * Validate leave balance data
     */
    LeaveBalanceUtils.validateBalance = function (balance) {
        var errors = [];
        if ('TotalAllowance' in balance && balance.TotalAllowance !== undefined) {
            if (balance.TotalAllowance < 0) {
                errors.push('Total allowance cannot be negative');
            }
            if (balance.TotalAllowance > 365) {
                errors.push('Total allowance cannot exceed 365 days');
            }
        }
        if ('CarryOverDays' in balance && balance.CarryOverDays !== undefined) {
            if (balance.CarryOverDays < 0) {
                errors.push('Carry over days cannot be negative');
            }
            if (balance.CarryOverDays > 30) {
                errors.push('Carry over days cannot exceed 30 days');
            }
        }
        if ('EffectiveDate' in balance && 'ExpirationDate' in balance &&
            balance.EffectiveDate && balance.ExpirationDate) {
            if (balance.ExpirationDate <= balance.EffectiveDate) {
                errors.push('Expiration date must be after effective date');
            }
        }
        return errors;
    };
    return LeaveBalanceUtils;
}());
export { LeaveBalanceUtils };
//# sourceMappingURL=ILeaveBalance.js.map