/**
 * Interface representing a Leave Type item from SharePoint
 */
export interface ILeaveType {
    Id: number;
    Title: string;
    Description?: string;
    IsActive: boolean;
    RequiresApproval: boolean;
    MaxDaysPerRequest?: number;
    RequiresDocumentation: boolean;
    ColorCode?: string;
    PolicyURL?: string;
    Created: Date;
    Modified: Date;
}
/**
 * Interface for creating a new Leave Type
 */
export interface ILeaveTypeCreate {
    Title: string;
    Description?: string;
    IsActive: boolean;
    RequiresApproval: boolean;
    MaxDaysPerRequest?: number;
    RequiresDocumentation: boolean;
    ColorCode?: string;
    PolicyURL?: string;
}
/**
 * Interface for updating a Leave Type
 */
export interface ILeaveTypeUpdate {
    Title?: string;
    Description?: string;
    IsActive?: boolean;
    RequiresApproval?: boolean;
    MaxDaysPerRequest?: number;
    RequiresDocumentation?: boolean;
    ColorCode?: string;
    PolicyURL?: string;
}
/**
 * Interface for Leave Type dropdown options
 */
export interface ILeaveTypeOption {
    key: number;
    text: string;
    data?: ILeaveType;
}
/**
 * Interface for Leave Type validation rules
 */
export interface ILeaveTypeValidationRules {
    maxDaysPerRequest?: number;
    requiresApproval: boolean;
    requiresDocumentation: boolean;
    isActive: boolean;
}
/**
 * Interface for Leave Type statistics
 */
export interface ILeaveTypeStats {
    leaveTypeId: number;
    leaveTypeName: string;
    totalRequests: number;
    totalDaysRequested: number;
    averageDaysPerRequest: number;
    mostRequestedMonth: string;
}
/**
 * Default Leave Types for initial setup
 */
export declare const DEFAULT_LEAVE_TYPES: ILeaveTypeCreate[];
/**
 * Utility functions for Leave Types
 */
export declare class LeaveTypeUtils {
    /**
     * Get active leave types only
     */
    static getActiveLeaveTypes(leaveTypes: ILeaveType[]): ILeaveType[];
    /**
     * Convert leave types to dropdown options
     */
    static toDropdownOptions(leaveTypes: ILeaveType[]): ILeaveTypeOption[];
    /**
     * Get leave type by ID
     */
    static getLeaveTypeById(leaveTypes: ILeaveType[], id: number): ILeaveType | undefined;
    /**
     * Validate color code format
     */
    static isValidColorCode(colorCode: string): boolean;
    /**
     * Get default color if none provided
     */
    static getDefaultColor(title: string): string;
}
//# sourceMappingURL=ILeaveType.d.ts.map