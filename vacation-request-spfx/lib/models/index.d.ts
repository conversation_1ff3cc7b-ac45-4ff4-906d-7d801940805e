/**
 * Export all model interfaces and utilities
 */
export * from './ILeaveRequest';
export * from './ILeaveType';
export * from './ILeaveBalance';
/**
 * Common interfaces used across the application
 */
/**
 * Interface for SharePoint user information
 */
export interface IUser {
    Id: number;
    Title: string;
    EMail: string;
    LoginName?: string;
    Department?: string;
    JobTitle?: string;
}
/**
 * Interface for SharePoint lookup field
 */
export interface ILookupValue {
    Id: number;
    Title: string;
}
/**
 * Interface for API response wrapper
 */
export interface IApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
/**
 * Interface for paginated results
 */
export interface IPaginatedResult<T> {
    items: T[];
    totalCount: number;
    hasMore: boolean;
    nextSkip?: number;
}
/**
 * Interface for sort options
 */
export interface ISortOption {
    field: string;
    direction: 'asc' | 'desc';
}
/**
 * Interface for common list operations
 */
export interface IListOperations<T, TCreate, TUpdate> {
    getAll(): Promise<T[]>;
    getById(id: number): Promise<T | null>;
    create(item: TCreate): Promise<T>;
    update(id: number, item: TUpdate): Promise<T>;
    delete(id: number): Promise<boolean>;
}
/**
 * Common constants
 */
export declare const LIST_NAMES: {
    readonly LEAVE_REQUESTS: "LeaveRequests";
    readonly LEAVE_TYPES: "LeaveTypes";
    readonly LEAVE_BALANCES: "LeaveBalances";
};
export declare const FIELD_NAMES: {
    readonly LEAVE_REQUESTS: {
        readonly REQUESTER: "Requester";
        readonly EMPLOYEE_ID: "EmployeeID";
        readonly DEPARTMENT: "Department";
        readonly MANAGER: "Manager";
        readonly LEAVE_TYPE: "LeaveType";
        readonly START_DATE: "StartDate";
        readonly END_DATE: "EndDate";
        readonly TOTAL_DAYS: "TotalDays";
        readonly IS_PARTIAL_DAY: "IsPartialDay";
        readonly PARTIAL_DAY_HOURS: "PartialDayHours";
        readonly REQUEST_COMMENTS: "RequestComments";
        readonly APPROVAL_STATUS: "ApprovalStatus";
        readonly APPROVAL_DATE: "ApprovalDate";
        readonly APPROVAL_COMMENTS: "ApprovalComments";
        readonly SUBMISSION_DATE: "SubmissionDate";
        readonly ATTACHMENT_URL: "AttachmentURL";
        readonly WORKFLOW_INSTANCE_ID: "WorkflowInstanceID";
        readonly NOTIFICATIONS_SENT: "NotificationsSent";
        readonly CALENDAR_EVENT_ID: "CalendarEventID";
    };
    readonly LEAVE_TYPES: {
        readonly TITLE: "Title";
        readonly DESCRIPTION: "Description";
        readonly IS_ACTIVE: "IsActive";
        readonly REQUIRES_APPROVAL: "RequiresApproval";
        readonly MAX_DAYS_PER_REQUEST: "MaxDaysPerRequest";
        readonly REQUIRES_DOCUMENTATION: "RequiresDocumentation";
        readonly COLOR_CODE: "ColorCode";
        readonly POLICY_URL: "PolicyURL";
    };
    readonly LEAVE_BALANCES: {
        readonly EMPLOYEE: "Employee";
        readonly LEAVE_TYPE: "LeaveType";
        readonly TOTAL_ALLOWANCE: "TotalAllowance";
        readonly USED_DAYS: "UsedDays";
        readonly REMAINING_DAYS: "RemainingDays";
        readonly CARRY_OVER_DAYS: "CarryOverDays";
        readonly EFFECTIVE_DATE: "EffectiveDate";
        readonly EXPIRATION_DATE: "ExpirationDate";
    };
};
/**
 * Utility functions for common operations
 */
export declare class CommonUtils {
    /**
     * Format date for SharePoint
     */
    static formatDateForSharePoint(date: Date): string;
    /**
     * Parse SharePoint date
     */
    static parseSharePointDate(dateString: string): Date;
    /**
     * Generate title for leave request
     */
    static generateLeaveRequestTitle(employeeName: string, leaveTypeName: string, startDate: Date): string;
    /**
     * Calculate business days between two dates
     */
    static calculateBusinessDays(startDate: Date, endDate: Date): number;
    /**
     * Validate email format
     */
    static isValidEmail(email: string): boolean;
    /**
     * Truncate text to specified length
     */
    static truncateText(text: string, maxLength: number): string;
    /**
     * Deep clone object
     */
    static deepClone<T>(obj: T): T;
    /**
     * Check if date is weekend
     */
    static isWeekend(date: Date): boolean;
    /**
     * Get next business day
     */
    static getNextBusinessDay(date: Date): Date;
}
//# sourceMappingURL=index.d.ts.map