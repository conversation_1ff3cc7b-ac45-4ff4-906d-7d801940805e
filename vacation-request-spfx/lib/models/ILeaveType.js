/**
 * Default Leave Types for initial setup
 */
export var DEFAULT_LEAVE_TYPES = [
    {
        Title: 'Annual Leave',
        Description: 'Standard annual vacation leave',
        IsActive: true,
        RequiresApproval: true,
        MaxDaysPerRequest: 30,
        RequiresDocumentation: false,
        ColorCode: '#4CAF50'
    },
    {
        Title: 'Sick Leave',
        Description: 'Medical leave for illness',
        IsActive: true,
        RequiresApproval: false,
        MaxDaysPerRequest: 5,
        RequiresDocumentation: true,
        ColorCode: '#FF9800'
    },
    {
        Title: 'Personal Leave',
        Description: 'Personal time off',
        IsActive: true,
        RequiresApproval: true,
        MaxDaysPerRequest: 10,
        RequiresDocumentation: false,
        ColorCode: '#2196F3'
    },
    {
        Title: 'Maternity/Paternity Leave',
        Description: 'Family leave for new parents',
        IsActive: true,
        RequiresApproval: true,
        MaxDaysPerRequest: 90,
        RequiresDocumentation: true,
        ColorCode: '#E91E63'
    },
    {
        Title: 'Emergency Leave',
        Description: 'Urgent personal matters',
        IsActive: true,
        RequiresApproval: false,
        MaxDaysPerRequest: 3,
        RequiresDocumentation: false,
        ColorCode: '#F44336'
    }
];
/**
 * Utility functions for Leave Types
 */
var LeaveTypeUtils = /** @class */ (function () {
    function LeaveTypeUtils() {
    }
    /**
     * Get active leave types only
     */
    LeaveTypeUtils.getActiveLeaveTypes = function (leaveTypes) {
        return leaveTypes.filter(function (lt) { return lt.IsActive; });
    };
    /**
     * Convert leave types to dropdown options
     */
    LeaveTypeUtils.toDropdownOptions = function (leaveTypes) {
        return leaveTypes
            .filter(function (lt) { return lt.IsActive; })
            .map(function (lt) { return ({
            key: lt.Id,
            text: lt.Title,
            data: lt
        }); });
    };
    /**
     * Get leave type by ID
     */
    LeaveTypeUtils.getLeaveTypeById = function (leaveTypes, id) {
        return leaveTypes.filter(function (lt) { return lt.Id === id; })[0];
    };
    /**
     * Validate color code format
     */
    LeaveTypeUtils.isValidColorCode = function (colorCode) {
        var colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        return colorRegex.test(colorCode);
    };
    /**
     * Get default color if none provided
     */
    LeaveTypeUtils.getDefaultColor = function (title) {
        var colors = ['#4CAF50', '#2196F3', '#FF9800', '#E91E63', '#9C27B0', '#607D8B'];
        var index = title.length % colors.length;
        return colors[index];
    };
    return LeaveTypeUtils;
}());
export { LeaveTypeUtils };
//# sourceMappingURL=ILeaveType.js.map