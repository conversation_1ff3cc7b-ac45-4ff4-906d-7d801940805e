/**
 * Interface representing a Leave Balance item from SharePoint
 */
export interface ILeaveBalance {
    Id: number;
    Employee: {
        Id: number;
        Title: string;
        EMail: string;
    };
    LeaveType: {
        Id: number;
        Title: string;
    };
    TotalAllowance: number;
    UsedDays: number;
    RemainingDays: number;
    CarryOverDays: number;
    EffectiveDate: Date;
    ExpirationDate: Date;
}
/**
 * Interface for creating a new Leave Balance
 */
export interface ILeaveBalanceCreate {
    EmployeeId: number;
    LeaveTypeId: number;
    TotalAllowance: number;
    CarryOverDays?: number;
    EffectiveDate: Date;
    ExpirationDate: Date;
}
/**
 * Interface for updating a Leave Balance
 */
export interface ILeaveBalanceUpdate {
    TotalAllowance?: number;
    UsedDays?: number;
    CarryOverDays?: number;
    EffectiveDate?: Date;
    ExpirationDate?: Date;
}
/**
 * Interface for Leave Balance summary
 */
export interface ILeaveBalanceSummary {
    employeeId: number;
    employeeName: string;
    balances: ILeaveBalanceDetail[];
    totalAllowance: number;
    totalUsed: number;
    totalRemaining: number;
}
/**
 * Interface for detailed Leave Balance information
 */
export interface ILeaveBalanceDetail {
    leaveTypeId: number;
    leaveTypeName: string;
    leaveTypeColor?: string;
    totalAllowance: number;
    usedDays: number;
    remainingDays: number;
    carryOverDays: number;
    effectiveDate: Date;
    expirationDate: Date;
    isExpiringSoon: boolean;
    daysUntilExpiry: number;
}
/**
 * Interface for Leave Balance calculation
 */
export interface ILeaveBalanceCalculation {
    leaveBalanceId: number;
    previousUsedDays: number;
    newUsedDays: number;
    previousRemainingDays: number;
    newRemainingDays: number;
    calculationDate: Date;
}
/**
 * Interface for Leave Balance filter options
 */
export interface ILeaveBalanceFilter {
    employeeId?: number;
    leaveTypeId?: number;
    isExpiringSoon?: boolean;
    hasRemainingDays?: boolean;
    effectiveDateFrom?: Date;
    effectiveDateTo?: Date;
    expirationDateFrom?: Date;
    expirationDateTo?: Date;
}
/**
 * Interface for Leave Balance analytics
 */
export interface ILeaveBalanceAnalytics {
    totalEmployees: number;
    totalLeaveTypes: number;
    averageAllowancePerEmployee: number;
    averageUsagePercentage: number;
    balancesExpiringSoon: number;
    topLeaveTypesByUsage: ILeaveTypeUsage[];
}
/**
 * Interface for Leave Type usage statistics
 */
export interface ILeaveTypeUsage {
    leaveTypeId: number;
    leaveTypeName: string;
    totalAllowance: number;
    totalUsed: number;
    usagePercentage: number;
    employeeCount: number;
}
/**
 * Utility functions for Leave Balances
 */
export declare class LeaveBalanceUtils {
    /**
     * Calculate remaining days
     */
    static calculateRemainingDays(totalAllowance: number, usedDays: number, carryOverDays?: number): number;
    /**
     * Check if balance is expiring soon (within 30 days)
     */
    static isExpiringSoon(expirationDate: Date, daysThreshold?: number): boolean;
    /**
     * Calculate days until expiry
     */
    static getDaysUntilExpiry(expirationDate: Date): number;
    /**
     * Calculate usage percentage
     */
    static calculateUsagePercentage(usedDays: number, totalAllowance: number, carryOverDays?: number): number;
    /**
     * Group balances by employee
     */
    static groupByEmployee(balances: ILeaveBalance[]): Map<number, ILeaveBalance[]>;
    /**
     * Create Leave Balance summary for an employee
     */
    static createEmployeeSummary(employeeBalances: ILeaveBalance[]): ILeaveBalanceSummary | null;
    /**
     * Validate leave balance data
     */
    static validateBalance(balance: ILeaveBalanceCreate | ILeaveBalanceUpdate): string[];
}
//# sourceMappingURL=ILeaveBalance.d.ts.map