/**
 * Export all services
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
export * from './SharePointService';
export * from './GraphService';
export * from './NotificationService';
export * from './ValidationService';
import { SharePointService } from './SharePointService';
import { GraphService } from './GraphService';
import { NotificationService } from './NotificationService';
var ServiceFactory = /** @class */ (function () {
    function ServiceFactory() {
    }
    /**
     * Create SharePoint service instance
     */
    ServiceFactory.createSharePointService = function (context) {
        return new SharePointService(context);
    };
    /**
     * Create Graph service instance
     */
    ServiceFactory.createGraphService = function (context) {
        return new GraphService(context);
    };
    /**
     * Create Notification service instance
     */
    ServiceFactory.createNotificationService = function (context) {
        return new NotificationService(context);
    };
    return ServiceFactory;
}());
export { ServiceFactory };
/**
 * Service manager for coordinating multiple services
 */
var ServiceManager = /** @class */ (function () {
    function ServiceManager(context) {
        this.sharePointService = new SharePointService(context);
        this.graphService = new GraphService(context);
        this.notificationService = new NotificationService(context);
    }
    /**
     * Get SharePoint service
     */
    ServiceManager.prototype.getSharePointService = function () {
        return this.sharePointService;
    };
    /**
     * Get Graph service
     */
    ServiceManager.prototype.getGraphService = function () {
        return this.graphService;
    };
    /**
     * Get Notification service
     */
    ServiceManager.prototype.getNotificationService = function () {
        return this.notificationService;
    };
    /**
     * Submit leave request with full workflow
     */
    ServiceManager.prototype.submitLeaveRequestWithWorkflow = function (request, sendNotifications) {
        if (sendNotifications === void 0) { sendNotifications = true; }
        return __awaiter(this, void 0, void 0, function () {
            var createdRequest, leaveType, eventId, calendarError_1, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 10, , 11]);
                        return [4 /*yield*/, this.sharePointService.createLeaveRequest(request)];
                    case 1:
                        createdRequest = _a.sent();
                        return [4 /*yield*/, this.sharePointService.getLeaveTypeById(request.LeaveTypeId)];
                    case 2:
                        leaveType = _a.sent();
                        if (!leaveType) {
                            throw new Error('Leave type not found');
                        }
                        if (!sendNotifications) return [3 /*break*/, 4];
                        return [4 /*yield*/, this.notificationService.sendSubmissionNotification(createdRequest, leaveType)];
                    case 3:
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        if (!!leaveType.RequiresApproval) return [3 /*break*/, 9];
                        _a.label = 5;
                    case 5:
                        _a.trys.push([5, 8, , 9]);
                        return [4 /*yield*/, this.graphService.createLeaveCalendarEvent(leaveType.Title, createdRequest.StartDate, createdRequest.EndDate, createdRequest.IsPartialDay, createdRequest.PartialDayHours, createdRequest.RequestComments)];
                    case 6:
                        eventId = _a.sent();
                        // Update request with calendar event ID
                        return [4 /*yield*/, this.sharePointService.updateLeaveRequest(createdRequest.Id, {
                                CalendarEventID: eventId,
                                ApprovalStatus: 'Approved',
                                ApprovalDate: new Date()
                            })];
                    case 7:
                        // Update request with calendar event ID
                        _a.sent();
                        return [3 /*break*/, 9];
                    case 8:
                        calendarError_1 = _a.sent();
                        console.warn('Could not create calendar event:', calendarError_1);
                        return [3 /*break*/, 9];
                    case 9: return [2 /*return*/, createdRequest];
                    case 10:
                        error_1 = _a.sent();
                        console.error('Error in submitLeaveRequestWithWorkflow:', error_1);
                        throw error_1;
                    case 11: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Approve leave request with full workflow
     */
    ServiceManager.prototype.approveLeaveRequestWithWorkflow = function (requestId, approverComments, sendNotifications) {
        if (sendNotifications === void 0) { sendNotifications = true; }
        return __awaiter(this, void 0, void 0, function () {
            var leaveRequest, leaveType, eventId, calendarError_2, updatedRequest, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 12, , 13]);
                        return [4 /*yield*/, this.sharePointService.getLeaveRequestById(requestId)];
                    case 1:
                        leaveRequest = _a.sent();
                        return [4 /*yield*/, this.sharePointService.getLeaveTypeById(leaveRequest.LeaveType.Id)];
                    case 2:
                        leaveType = _a.sent();
                        if (!leaveType) {
                            throw new Error('Leave type not found');
                        }
                        // 3. Update request status
                        return [4 /*yield*/, this.sharePointService.updateLeaveRequest(requestId, {
                                ApprovalStatus: 'Approved',
                                ApprovalDate: new Date(),
                                ApprovalComments: approverComments
                            })];
                    case 3:
                        // 3. Update request status
                        _a.sent();
                        _a.label = 4;
                    case 4:
                        _a.trys.push([4, 7, , 8]);
                        return [4 /*yield*/, this.graphService.createLeaveCalendarEvent(leaveType.Title, leaveRequest.StartDate, leaveRequest.EndDate, leaveRequest.IsPartialDay, leaveRequest.PartialDayHours, leaveRequest.RequestComments)];
                    case 5:
                        eventId = _a.sent();
                        // Update request with calendar event ID
                        return [4 /*yield*/, this.sharePointService.updateLeaveRequest(requestId, {
                                CalendarEventID: eventId
                            })];
                    case 6:
                        // Update request with calendar event ID
                        _a.sent();
                        return [3 /*break*/, 8];
                    case 7:
                        calendarError_2 = _a.sent();
                        console.warn('Could not create calendar event:', calendarError_2);
                        return [3 /*break*/, 8];
                    case 8:
                        if (!sendNotifications) return [3 /*break*/, 11];
                        return [4 /*yield*/, this.sharePointService.getLeaveRequestById(requestId)];
                    case 9:
                        updatedRequest = _a.sent();
                        return [4 /*yield*/, this.notificationService.sendApprovalNotification(updatedRequest, leaveType, true, approverComments)];
                    case 10:
                        _a.sent();
                        _a.label = 11;
                    case 11: return [3 /*break*/, 13];
                    case 12:
                        error_2 = _a.sent();
                        console.error('Error in approveLeaveRequestWithWorkflow:', error_2);
                        throw error_2;
                    case 13: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Reject leave request with full workflow
     */
    ServiceManager.prototype.rejectLeaveRequestWithWorkflow = function (requestId, approverComments, sendNotifications) {
        if (sendNotifications === void 0) { sendNotifications = true; }
        return __awaiter(this, void 0, void 0, function () {
            var leaveRequest, leaveType, updatedRequest, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 7, , 8]);
                        return [4 /*yield*/, this.sharePointService.getLeaveRequestById(requestId)];
                    case 1:
                        leaveRequest = _a.sent();
                        return [4 /*yield*/, this.sharePointService.getLeaveTypeById(leaveRequest.LeaveType.Id)];
                    case 2:
                        leaveType = _a.sent();
                        if (!leaveType) {
                            throw new Error('Leave type not found');
                        }
                        // 3. Update request status
                        return [4 /*yield*/, this.sharePointService.updateLeaveRequest(requestId, {
                                ApprovalStatus: 'Rejected',
                                ApprovalDate: new Date(),
                                ApprovalComments: approverComments
                            })];
                    case 3:
                        // 3. Update request status
                        _a.sent();
                        if (!sendNotifications) return [3 /*break*/, 6];
                        return [4 /*yield*/, this.sharePointService.getLeaveRequestById(requestId)];
                    case 4:
                        updatedRequest = _a.sent();
                        return [4 /*yield*/, this.notificationService.sendApprovalNotification(updatedRequest, leaveType, false, approverComments)];
                    case 5:
                        _a.sent();
                        _a.label = 6;
                    case 6: return [3 /*break*/, 8];
                    case 7:
                        error_3 = _a.sent();
                        console.error('Error in rejectLeaveRequestWithWorkflow:', error_3);
                        throw error_3;
                    case 8: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Cancel leave request with cleanup
     */
    ServiceManager.prototype.cancelLeaveRequestWithCleanup = function (requestId, sendNotifications) {
        if (sendNotifications === void 0) { sendNotifications = true; }
        return __awaiter(this, void 0, void 0, function () {
            var leaveRequest, calendarError_3, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 7, , 8]);
                        return [4 /*yield*/, this.sharePointService.getLeaveRequestById(requestId)];
                    case 1:
                        leaveRequest = _a.sent();
                        if (!leaveRequest.CalendarEventID) return [3 /*break*/, 5];
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, this.graphService.deleteCalendarEvent(leaveRequest.CalendarEventID)];
                    case 3:
                        _a.sent();
                        return [3 /*break*/, 5];
                    case 4:
                        calendarError_3 = _a.sent();
                        console.warn('Could not delete calendar event:', calendarError_3);
                        return [3 /*break*/, 5];
                    case 5: 
                    // 3. Update request status
                    return [4 /*yield*/, this.sharePointService.updateLeaveRequest(requestId, {
                            ApprovalStatus: 'Cancelled',
                            CalendarEventID: undefined
                        })];
                    case 6:
                        // 3. Update request status
                        _a.sent();
                        // 4. Send notifications if needed
                        if (sendNotifications) {
                            // Implementation for cancellation notifications
                            console.log('Leave request cancelled:', requestId);
                        }
                        return [3 /*break*/, 8];
                    case 7:
                        error_4 = _a.sent();
                        console.error('Error in cancelLeaveRequestWithCleanup:', error_4);
                        throw error_4;
                    case 8: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get comprehensive user dashboard data
     */
    ServiceManager.prototype.getUserDashboardData = function (userId) {
        return __awaiter(this, void 0, void 0, function () {
            var _a, leaveRequests, leaveBalances, leaveTypes, userProfile, error_5;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, Promise.all([
                                this.sharePointService.getCurrentUserLeaveRequests(),
                                this.sharePointService.getCurrentUserLeaveBalances(),
                                this.sharePointService.getLeaveTypes(),
                                this.graphService.getCurrentUserProfile()
                            ])];
                    case 1:
                        _a = _b.sent(), leaveRequests = _a[0], leaveBalances = _a[1], leaveTypes = _a[2], userProfile = _a[3];
                        return [2 /*return*/, {
                                leaveRequests: leaveRequests,
                                leaveBalances: leaveBalances,
                                leaveTypes: leaveTypes,
                                userProfile: userProfile,
                                summary: {
                                    totalRequests: leaveRequests.length,
                                    pendingRequests: leaveRequests.filter(function (r) { return r.ApprovalStatus === 'Pending'; }).length,
                                    approvedRequests: leaveRequests.filter(function (r) { return r.ApprovalStatus === 'Approved'; }).length,
                                    totalBalance: leaveBalances.reduce(function (sum, b) { return sum + b.RemainingDays; }, 0)
                                }
                            }];
                    case 2:
                        error_5 = _b.sent();
                        console.error('Error getting user dashboard data:', error_5);
                        throw error_5;
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    return ServiceManager;
}());
export { ServiceManager };
//# sourceMappingURL=index.js.map