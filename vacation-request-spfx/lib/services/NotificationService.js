var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
import { GraphService } from './GraphService';
/**
 * Service class for handling notifications
 */
var NotificationService = /** @class */ (function () {
    function NotificationService(context) {
        this.context = context;
        this.graphService = new GraphService(context);
    }
    /**
     * Send leave request submission notification
     */
    NotificationService.prototype.sendSubmissionNotification = function (leaveRequest, leaveType) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var template, recipients, error_1;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 2, , 3]);
                        template = this.getSubmissionTemplate(leaveRequest, leaveType);
                        recipients = [leaveRequest.Requester.EMail];
                        // Add manager email if available
                        if ((_a = leaveRequest.Manager) === null || _a === void 0 ? void 0 : _a.EMail) {
                            recipients.push(leaveRequest.Manager.EMail);
                        }
                        return [4 /*yield*/, this.graphService.sendNotificationEmail(recipients, template.subject, template.body, template.isHtml)];
                    case 1:
                        _b.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        error_1 = _b.sent();
                        console.error('Error sending submission notification:', error_1);
                        throw new Error('Failed to send submission notification');
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Send approval notification
     */
    NotificationService.prototype.sendApprovalNotification = function (leaveRequest, leaveType, isApproved, approverComments) {
        return __awaiter(this, void 0, void 0, function () {
            var template, recipients, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        template = this.getApprovalTemplate(leaveRequest, leaveType, isApproved, approverComments);
                        recipients = [leaveRequest.Requester.EMail];
                        return [4 /*yield*/, this.graphService.sendNotificationEmail(recipients, template.subject, template.body, template.isHtml)];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        error_2 = _a.sent();
                        console.error('Error sending approval notification:', error_2);
                        throw new Error('Failed to send approval notification');
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Send reminder notification for pending approvals
     */
    NotificationService.prototype.sendPendingApprovalReminder = function (leaveRequest, leaveType) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var template, error_3;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 2, , 3]);
                        if (!((_a = leaveRequest.Manager) === null || _a === void 0 ? void 0 : _a.EMail)) {
                            console.warn('No manager email available for reminder');
                            return [2 /*return*/];
                        }
                        template = this.getPendingApprovalReminderTemplate(leaveRequest, leaveType);
                        return [4 /*yield*/, this.graphService.sendNotificationEmail([leaveRequest.Manager.EMail], template.subject, template.body, template.isHtml)];
                    case 1:
                        _b.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        error_3 = _b.sent();
                        console.error('Error sending pending approval reminder:', error_3);
                        throw new Error('Failed to send pending approval reminder');
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Send leave balance warning notification
     */
    NotificationService.prototype.sendBalanceWarningNotification = function (userEmail, leaveTypeName, remainingDays, expirationDate) {
        return __awaiter(this, void 0, void 0, function () {
            var template, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        template = this.getBalanceWarningTemplate(leaveTypeName, remainingDays, expirationDate);
                        return [4 /*yield*/, this.graphService.sendNotificationEmail([userEmail], template.subject, template.body, template.isHtml)];
                    case 1:
                        _a.sent();
                        return [3 /*break*/, 3];
                    case 2:
                        error_4 = _a.sent();
                        console.error('Error sending balance warning notification:', error_4);
                        throw new Error('Failed to send balance warning notification');
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get submission notification template
     */
    NotificationService.prototype.getSubmissionTemplate = function (leaveRequest, leaveType) {
        var startDate = leaveRequest.StartDate.toLocaleDateString();
        var endDate = leaveRequest.EndDate.toLocaleDateString();
        var totalDays = leaveRequest.TotalDays || 1;
        var subject = "Leave Request Submitted - ".concat(leaveType.Title);
        var body = "\nDear ".concat(leaveRequest.Requester.Title, ",\n\nYour leave request has been successfully submitted and is now pending approval.\n\nRequest Details:\n- Leave Type: ").concat(leaveType.Title, "\n- Start Date: ").concat(startDate, "\n- End Date: ").concat(endDate, "\n- Total Days: ").concat(totalDays, "\n").concat(leaveRequest.IsPartialDay ? "- Partial Day Hours: ".concat(leaveRequest.PartialDayHours) : '', "\n").concat(leaveRequest.RequestComments ? "- Comments: ".concat(leaveRequest.RequestComments) : '', "\n\n").concat(leaveType.RequiresApproval ?
            "Your request will be reviewed by your manager and you will be notified of the decision." :
            "This leave type does not require approval and has been automatically approved.", "\n\nYou can track the status of your request in the Leave Request system.\n\nBest regards,\nHR Team\n    ").trim();
        return {
            subject: subject,
            body: body,
            isHtml: false
        };
    };
    /**
     * Get approval notification template
     */
    NotificationService.prototype.getApprovalTemplate = function (leaveRequest, leaveType, isApproved, approverComments) {
        var startDate = leaveRequest.StartDate.toLocaleDateString();
        var endDate = leaveRequest.EndDate.toLocaleDateString();
        var status = isApproved ? 'APPROVED' : 'REJECTED';
        var statusText = isApproved ? 'approved' : 'rejected';
        var subject = "Leave Request ".concat(status, " - ").concat(leaveType.Title);
        var body = "\nDear ".concat(leaveRequest.Requester.Title, ",\n\nYour leave request has been ").concat(statusText, ".\n\nRequest Details:\n- Leave Type: ").concat(leaveType.Title, "\n- Start Date: ").concat(startDate, "\n- End Date: ").concat(endDate, "\n- Total Days: ").concat(leaveRequest.TotalDays || 1, "\n- Status: ").concat(status, "\n").concat(approverComments ? "- Manager Comments: ".concat(approverComments) : '', "\n\n").concat(isApproved ?
            "Your leave has been approved and a calendar event has been created. Please ensure proper handover of your responsibilities before your leave begins." :
            "If you have questions about this decision, please contact your manager or HR.", "\n\nBest regards,\nHR Team\n    ").trim();
        return {
            subject: subject,
            body: body,
            isHtml: false
        };
    };
    /**
     * Get pending approval reminder template
     */
    NotificationService.prototype.getPendingApprovalReminderTemplate = function (leaveRequest, leaveType) {
        var _a;
        var startDate = leaveRequest.StartDate.toLocaleDateString();
        var endDate = leaveRequest.EndDate.toLocaleDateString();
        var submissionDate = leaveRequest.SubmissionDate.toLocaleDateString();
        var subject = "Reminder: Pending Leave Request Approval - ".concat(leaveRequest.Requester.Title);
        var body = "\nDear ".concat(((_a = leaveRequest.Manager) === null || _a === void 0 ? void 0 : _a.Title) || 'Manager', ",\n\nThis is a reminder that you have a pending leave request that requires your approval.\n\nRequest Details:\n- Employee: ").concat(leaveRequest.Requester.Title, "\n- Leave Type: ").concat(leaveType.Title, "\n- Start Date: ").concat(startDate, "\n- End Date: ").concat(endDate, "\n- Total Days: ").concat(leaveRequest.TotalDays || 1, "\n- Submitted: ").concat(submissionDate, "\n").concat(leaveRequest.RequestComments ? "- Comments: ".concat(leaveRequest.RequestComments) : '', "\n\nPlease review and approve or reject this request at your earliest convenience.\n\nYou can access the Leave Request system to take action on this request.\n\nBest regards,\nHR Team\n    ").trim();
        return {
            subject: subject,
            body: body,
            isHtml: false
        };
    };
    /**
     * Get balance warning notification template
     */
    NotificationService.prototype.getBalanceWarningTemplate = function (leaveTypeName, remainingDays, expirationDate) {
        var expirationDateStr = expirationDate.toLocaleDateString();
        var subject = "Leave Balance Expiration Warning - ".concat(leaveTypeName);
        var body = "\nDear Team Member,\n\nThis is a reminder that you have unused leave balance that will expire soon.\n\nLeave Balance Details:\n- Leave Type: ".concat(leaveTypeName, "\n- Remaining Days: ").concat(remainingDays, "\n- Expiration Date: ").concat(expirationDateStr, "\n\nPlease consider using your remaining leave days before they expire. You can submit a leave request through the Leave Request system.\n\nIf you have any questions about your leave balance or policies, please contact HR.\n\nBest regards,\nHR Team\n    ").trim();
        return {
            subject: subject,
            body: body,
            isHtml: false
        };
    };
    /**
     * Create notification for Teams (if available)
     */
    NotificationService.prototype.sendTeamsNotification = function (message, title) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                try {
                    // Check if running in Teams context
                    if (this.context.sdks.microsoftTeams) {
                        // This would require additional Teams SDK integration
                        console.log('Teams notification:', { title: title, message: message });
                        // Implementation would depend on Teams notification capabilities
                    }
                }
                catch (error) {
                    console.error('Error sending Teams notification:', error);
                }
                return [2 /*return*/];
            });
        });
    };
    /**
     * Batch send notifications for multiple requests
     */
    NotificationService.prototype.sendBatchNotifications = function (notifications) {
        return __awaiter(this, void 0, void 0, function () {
            var promises, _i, promises_1, promise, error_5;
            var _this = this;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        promises = notifications.map(function (notification) { return __awaiter(_this, void 0, void 0, function () {
                            var _a, error_6;
                            return __generator(this, function (_b) {
                                switch (_b.label) {
                                    case 0:
                                        _b.trys.push([0, 10, , 11]);
                                        _a = notification.type;
                                        switch (_a) {
                                            case 'submission': return [3 /*break*/, 1];
                                            case 'approval': return [3 /*break*/, 3];
                                            case 'reminder': return [3 /*break*/, 5];
                                            case 'balance-warning': return [3 /*break*/, 7];
                                        }
                                        return [3 /*break*/, 9];
                                    case 1: return [4 /*yield*/, this.sendSubmissionNotification(notification.data.leaveRequest, notification.data.leaveType)];
                                    case 2:
                                        _b.sent();
                                        return [3 /*break*/, 9];
                                    case 3: return [4 /*yield*/, this.sendApprovalNotification(notification.data.leaveRequest, notification.data.leaveType, notification.data.isApproved, notification.data.approverComments)];
                                    case 4:
                                        _b.sent();
                                        return [3 /*break*/, 9];
                                    case 5: return [4 /*yield*/, this.sendPendingApprovalReminder(notification.data.leaveRequest, notification.data.leaveType)];
                                    case 6:
                                        _b.sent();
                                        return [3 /*break*/, 9];
                                    case 7: return [4 /*yield*/, this.sendBalanceWarningNotification(notification.data.userEmail, notification.data.leaveTypeName, notification.data.remainingDays, notification.data.expirationDate)];
                                    case 8:
                                        _b.sent();
                                        return [3 /*break*/, 9];
                                    case 9: return [3 /*break*/, 11];
                                    case 10:
                                        error_6 = _b.sent();
                                        console.error("Error sending ".concat(notification.type, " notification:"), error_6);
                                        return [3 /*break*/, 11];
                                    case 11: return [2 /*return*/];
                                }
                            });
                        }); });
                        _i = 0, promises_1 = promises;
                        _a.label = 1;
                    case 1:
                        if (!(_i < promises_1.length)) return [3 /*break*/, 6];
                        promise = promises_1[_i];
                        _a.label = 2;
                    case 2:
                        _a.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, promise];
                    case 3:
                        _a.sent();
                        return [3 /*break*/, 5];
                    case 4:
                        error_5 = _a.sent();
                        return [3 /*break*/, 5];
                    case 5:
                        _i++;
                        return [3 /*break*/, 1];
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    return NotificationService;
}());
export { NotificationService };
//# sourceMappingURL=NotificationService.js.map