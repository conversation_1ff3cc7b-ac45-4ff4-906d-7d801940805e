var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
import { spfi, SPFx } from '@pnp/sp';
import '@pnp/sp/webs';
import '@pnp/sp/lists';
import '@pnp/sp/items';
import { LIST_NAMES, FIELD_NAMES, ApprovalStatus, CommonUtils } from '../models';
/**
 * Service class for SharePoint operations
 */
var SharePointService = /** @class */ (function () {
    function SharePointService(context) {
        this.context = context;
        this.sp = spfi().using(SPFx(context));
    }
    /**
     * Get all active leave types
     */
    SharePointService.prototype.getLeaveTypes = function () {
        return __awaiter(this, void 0, void 0, function () {
            var items, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, this.sp.web.lists
                                .getByTitle(LIST_NAMES.LEAVE_TYPES)
                                .items
                                .select('Id', 'Title', 'Description', 'IsActive', 'RequiresApproval', 'MaxDaysPerRequest', 'RequiresDocumentation', 'ColorCode', 'PolicyURL', 'Created', 'Modified')
                                .filter('IsActive eq true')
                                .orderBy('Title')()];
                    case 1:
                        items = _a.sent();
                        return [2 /*return*/, items.map(function (item) { return ({
                                Id: item.Id,
                                Title: item.Title,
                                Description: item.Description,
                                IsActive: item.IsActive,
                                RequiresApproval: item.RequiresApproval,
                                MaxDaysPerRequest: item.MaxDaysPerRequest,
                                RequiresDocumentation: item.RequiresDocumentation,
                                ColorCode: item.ColorCode,
                                PolicyURL: item.PolicyURL,
                                Created: new Date(item.Created),
                                Modified: new Date(item.Modified)
                            }); })];
                    case 2:
                        error_1 = _a.sent();
                        console.error('Error fetching leave types:', error_1);
                        throw new Error('Failed to fetch leave types');
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get leave type by ID
     */
    SharePointService.prototype.getLeaveTypeById = function (id) {
        return __awaiter(this, void 0, void 0, function () {
            var item, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, this.sp.web.lists
                                .getByTitle(LIST_NAMES.LEAVE_TYPES)
                                .items
                                .getById(id)
                                .select('Id', 'Title', 'Description', 'IsActive', 'RequiresApproval', 'MaxDaysPerRequest', 'RequiresDocumentation', 'ColorCode', 'PolicyURL', 'Created', 'Modified')()];
                    case 1:
                        item = _a.sent();
                        return [2 /*return*/, {
                                Id: item.Id,
                                Title: item.Title,
                                Description: item.Description,
                                IsActive: item.IsActive,
                                RequiresApproval: item.RequiresApproval,
                                MaxDaysPerRequest: item.MaxDaysPerRequest,
                                RequiresDocumentation: item.RequiresDocumentation,
                                ColorCode: item.ColorCode,
                                PolicyURL: item.PolicyURL,
                                Created: new Date(item.Created),
                                Modified: new Date(item.Modified)
                            }];
                    case 2:
                        error_2 = _a.sent();
                        console.error('Error fetching leave type:', error_2);
                        return [2 /*return*/, null];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Create a new leave request
     */
    SharePointService.prototype.createLeaveRequest = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var title, itemData, result, error_3;
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _b.trys.push([0, 3, , 4]);
                        title = request.Title || CommonUtils.generateLeaveRequestTitle(this.context.pageContext.user.displayName, 'Leave Request', request.StartDate);
                        itemData = (_a = {
                                Title: title
                            },
                            _a[FIELD_NAMES.LEAVE_REQUESTS.LEAVE_TYPE + 'Id'] = request.LeaveTypeId,
                            _a[FIELD_NAMES.LEAVE_REQUESTS.START_DATE] = CommonUtils.formatDateForSharePoint(request.StartDate),
                            _a[FIELD_NAMES.LEAVE_REQUESTS.END_DATE] = CommonUtils.formatDateForSharePoint(request.EndDate),
                            _a[FIELD_NAMES.LEAVE_REQUESTS.IS_PARTIAL_DAY] = request.IsPartialDay,
                            _a[FIELD_NAMES.LEAVE_REQUESTS.PARTIAL_DAY_HOURS] = request.PartialDayHours,
                            _a[FIELD_NAMES.LEAVE_REQUESTS.REQUEST_COMMENTS] = request.RequestComments,
                            _a[FIELD_NAMES.LEAVE_REQUESTS.ATTACHMENT_URL] = request.AttachmentURL,
                            _a[FIELD_NAMES.LEAVE_REQUESTS.REQUESTER + 'Id'] = this.context.pageContext.user.loginName,
                            _a[FIELD_NAMES.LEAVE_REQUESTS.APPROVAL_STATUS] = ApprovalStatus.Pending,
                            _a[FIELD_NAMES.LEAVE_REQUESTS.SUBMISSION_DATE] = CommonUtils.formatDateForSharePoint(new Date()),
                            _a[FIELD_NAMES.LEAVE_REQUESTS.NOTIFICATIONS_SENT] = false,
                            _a);
                        return [4 /*yield*/, this.sp.web.lists
                                .getByTitle(LIST_NAMES.LEAVE_REQUESTS)
                                .items
                                .add(itemData)];
                    case 1:
                        result = _b.sent();
                        return [4 /*yield*/, this.getLeaveRequestById(result.data.Id)];
                    case 2: 
                    // Fetch the created item with all fields
                    return [2 /*return*/, _b.sent()];
                    case 3:
                        error_3 = _b.sent();
                        console.error('Error creating leave request:', error_3);
                        throw new Error('Failed to create leave request');
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get leave request by ID
     */
    SharePointService.prototype.getLeaveRequestById = function (id) {
        var _a, _b, _c, _d, _e;
        return __awaiter(this, void 0, void 0, function () {
            var item, error_4;
            return __generator(this, function (_f) {
                switch (_f.label) {
                    case 0:
                        _f.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, this.sp.web.lists
                                .getByTitle(LIST_NAMES.LEAVE_REQUESTS)
                                .items
                                .getById(id)
                                .select('Id', 'Title', 'StartDate', 'EndDate', 'TotalDays', 'IsPartialDay', 'PartialDayHours', 'RequestComments', 'ApprovalStatus', 'ApprovalDate', 'ApprovalComments', 'SubmissionDate', 'Modified', 'AttachmentURL', 'NotificationsSent', 'Requester/Id', 'Requester/Title', 'Requester/EMail', 'Manager/Id', 'Manager/Title', 'Manager/EMail', 'LeaveType/Id', 'LeaveType/Title')
                                .expand('Requester', 'Manager', 'LeaveType')()];
                    case 1:
                        item = _f.sent();
                        return [2 /*return*/, {
                                Id: item.Id,
                                Title: item.Title,
                                Requester: {
                                    Id: ((_a = item.Requester) === null || _a === void 0 ? void 0 : _a.Id) || 0,
                                    Title: ((_b = item.Requester) === null || _b === void 0 ? void 0 : _b.Title) || '',
                                    EMail: ((_c = item.Requester) === null || _c === void 0 ? void 0 : _c.EMail) || ''
                                },
                                Manager: item.Manager ? {
                                    Id: item.Manager.Id,
                                    Title: item.Manager.Title,
                                    EMail: item.Manager.EMail
                                } : undefined,
                                LeaveType: {
                                    Id: ((_d = item.LeaveType) === null || _d === void 0 ? void 0 : _d.Id) || 0,
                                    Title: ((_e = item.LeaveType) === null || _e === void 0 ? void 0 : _e.Title) || ''
                                },
                                StartDate: new Date(item.StartDate),
                                EndDate: new Date(item.EndDate),
                                TotalDays: item.TotalDays,
                                IsPartialDay: item.IsPartialDay,
                                PartialDayHours: item.PartialDayHours,
                                RequestComments: item.RequestComments,
                                ApprovalStatus: item.ApprovalStatus,
                                ApprovalDate: item.ApprovalDate ? new Date(item.ApprovalDate) : undefined,
                                ApprovalComments: item.ApprovalComments,
                                SubmissionDate: new Date(item.SubmissionDate),
                                LastModified: new Date(item.Modified),
                                AttachmentURL: item.AttachmentURL,
                                NotificationsSent: item.NotificationsSent
                            }];
                    case 2:
                        error_4 = _f.sent();
                        console.error('Error fetching leave request:', error_4);
                        throw new Error('Failed to fetch leave request');
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get current user's leave requests
     */
    SharePointService.prototype.getCurrentUserLeaveRequests = function () {
        return __awaiter(this, void 0, void 0, function () {
            var items, error_5;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, this.sp.web.lists
                                .getByTitle(LIST_NAMES.LEAVE_REQUESTS)
                                .items
                                .select('Id', 'Title', 'StartDate', 'EndDate', 'TotalDays', 'IsPartialDay', 'PartialDayHours', 'RequestComments', 'ApprovalStatus', 'ApprovalDate', 'ApprovalComments', 'SubmissionDate', 'Modified', 'AttachmentURL', 'NotificationsSent', 'Requester/Id', 'Requester/Title', 'Requester/EMail', 'Manager/Id', 'Manager/Title', 'Manager/EMail', 'LeaveType/Id', 'LeaveType/Title')
                                .expand('Requester', 'Manager', 'LeaveType')
                                .filter("Requester/Id eq ".concat(this.context.pageContext.user.loginName))
                                .orderBy('SubmissionDate', false)()];
                    case 1:
                        items = _a.sent();
                        return [2 /*return*/, items.map(function (item) {
                                var _a, _b, _c, _d, _e;
                                return ({
                                    Id: item.Id,
                                    Title: item.Title,
                                    Requester: {
                                        Id: ((_a = item.Requester) === null || _a === void 0 ? void 0 : _a.Id) || 0,
                                        Title: ((_b = item.Requester) === null || _b === void 0 ? void 0 : _b.Title) || '',
                                        EMail: ((_c = item.Requester) === null || _c === void 0 ? void 0 : _c.EMail) || ''
                                    },
                                    Manager: item.Manager ? {
                                        Id: item.Manager.Id,
                                        Title: item.Manager.Title,
                                        EMail: item.Manager.EMail
                                    } : undefined,
                                    LeaveType: {
                                        Id: ((_d = item.LeaveType) === null || _d === void 0 ? void 0 : _d.Id) || 0,
                                        Title: ((_e = item.LeaveType) === null || _e === void 0 ? void 0 : _e.Title) || ''
                                    },
                                    StartDate: new Date(item.StartDate),
                                    EndDate: new Date(item.EndDate),
                                    TotalDays: item.TotalDays,
                                    IsPartialDay: item.IsPartialDay,
                                    PartialDayHours: item.PartialDayHours,
                                    RequestComments: item.RequestComments,
                                    ApprovalStatus: item.ApprovalStatus,
                                    ApprovalDate: item.ApprovalDate ? new Date(item.ApprovalDate) : undefined,
                                    ApprovalComments: item.ApprovalComments,
                                    SubmissionDate: new Date(item.SubmissionDate),
                                    LastModified: new Date(item.Modified),
                                    AttachmentURL: item.AttachmentURL,
                                    NotificationsSent: item.NotificationsSent
                                });
                            })];
                    case 2:
                        error_5 = _a.sent();
                        console.error('Error fetching user leave requests:', error_5);
                        throw new Error('Failed to fetch leave requests');
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get current user's leave balances
     */
    SharePointService.prototype.getCurrentUserLeaveBalances = function () {
        return __awaiter(this, void 0, void 0, function () {
            var items, error_6;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, this.sp.web.lists
                                .getByTitle(LIST_NAMES.LEAVE_BALANCES)
                                .items
                                .select('Id', 'TotalAllowance', 'UsedDays', 'RemainingDays', 'CarryOverDays', 'EffectiveDate', 'ExpirationDate', 'Employee/Id', 'Employee/Title', 'Employee/EMail', 'LeaveType/Id', 'LeaveType/Title')
                                .expand('Employee', 'LeaveType')
                                .filter("Employee/Id eq ".concat(this.context.pageContext.user.loginName))()];
                    case 1:
                        items = _a.sent();
                        return [2 /*return*/, items.map(function (item) {
                                var _a, _b, _c, _d, _e;
                                return ({
                                    Id: item.Id,
                                    Employee: {
                                        Id: ((_a = item.Employee) === null || _a === void 0 ? void 0 : _a.Id) || 0,
                                        Title: ((_b = item.Employee) === null || _b === void 0 ? void 0 : _b.Title) || '',
                                        EMail: ((_c = item.Employee) === null || _c === void 0 ? void 0 : _c.EMail) || ''
                                    },
                                    LeaveType: {
                                        Id: ((_d = item.LeaveType) === null || _d === void 0 ? void 0 : _d.Id) || 0,
                                        Title: ((_e = item.LeaveType) === null || _e === void 0 ? void 0 : _e.Title) || ''
                                    },
                                    TotalAllowance: item.TotalAllowance,
                                    UsedDays: item.UsedDays,
                                    RemainingDays: item.RemainingDays,
                                    CarryOverDays: item.CarryOverDays,
                                    EffectiveDate: new Date(item.EffectiveDate),
                                    ExpirationDate: new Date(item.ExpirationDate)
                                });
                            })];
                    case 2:
                        error_6 = _a.sent();
                        console.error('Error fetching leave balances:', error_6);
                        throw new Error('Failed to fetch leave balances');
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Validate leave request against business rules
     */
    SharePointService.prototype.validateLeaveRequest = function (request) {
        return __awaiter(this, void 0, void 0, function () {
            var errors, leaveType, totalDays, today, error_7;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        errors = [];
                        _a.label = 1;
                    case 1:
                        _a.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, this.getLeaveTypeById(request.LeaveTypeId)];
                    case 2:
                        leaveType = _a.sent();
                        if (!leaveType) {
                            errors.push('Invalid leave type selected');
                            return [2 /*return*/, { isValid: false, errors: errors }];
                        }
                        // Validate dates
                        if (request.EndDate < request.StartDate) {
                            errors.push('End date must be after start date');
                        }
                        // Validate partial day hours
                        if (request.IsPartialDay) {
                            if (!request.PartialDayHours || request.PartialDayHours <= 0 || request.PartialDayHours > 8) {
                                errors.push('Partial day hours must be between 0.5 and 8 hours');
                            }
                        }
                        // Validate max days per request
                        if (leaveType.MaxDaysPerRequest) {
                            totalDays = CommonUtils.calculateBusinessDays(request.StartDate, request.EndDate);
                            if (totalDays > leaveType.MaxDaysPerRequest) {
                                errors.push("Maximum ".concat(leaveType.MaxDaysPerRequest, " days allowed for ").concat(leaveType.Title));
                            }
                        }
                        // Validate documentation requirement
                        if (leaveType.RequiresDocumentation && !request.AttachmentURL) {
                            errors.push("Documentation is required for ".concat(leaveType.Title));
                        }
                        today = new Date();
                        today.setHours(0, 0, 0, 0);
                        if (request.StartDate < today && leaveType.Title !== 'Emergency Leave') {
                            errors.push('Cannot request leave for past dates');
                        }
                        return [2 /*return*/, { isValid: errors.length === 0, errors: errors }];
                    case 3:
                        error_7 = _a.sent();
                        console.error('Error validating leave request:', error_7);
                        return [2 /*return*/, { isValid: false, errors: ['Validation failed'] }];
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return SharePointService;
}());
export { SharePointService };
//# sourceMappingURL=SharePointService.js.map