import { ILeaveRequestCreate, ILeaveType, ILeaveBalance } from '../models';
/**
 * Interface for validation result
 */
export interface IValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}
/**
 * Interface for conflict detection result
 */
export interface IConflictResult {
    hasConflicts: boolean;
    conflicts: IConflictDetail[];
}
/**
 * Interface for conflict detail
 */
export interface IConflictDetail {
    type: 'team-member' | 'blackout-date' | 'holiday' | 'overlap';
    message: string;
    severity: 'error' | 'warning';
    details?: any;
}
/**
 * Service class for validation and business rules
 */
export declare class ValidationService {
    /**
     * Validate leave request against business rules
     */
    static validateLeaveRequest(request: ILeaveRequestCreate, leaveType: ILeaveType, leaveBalance?: ILeaveBalance, existingRequests?: any[]): IValidationResult;
    /**
     * Validate basic required fields
     */
    private static validateBasicFields;
    /**
     * Validate dates
     */
    private static validateDates;
    /**
     * Validate against leave type rules
     */
    private static validateLeaveType;
    /**
     * Validate against leave balance
     */
    private static validateBalance;
    /**
     * Validate for overlapping requests
     */
    private static validateOverlaps;
    /**
     * Detect conflicts with team members and blackout dates
     */
    static detectConflicts(request: ILeaveRequestCreate, teamMembers: any[], blackoutDates: Date[], holidays: Date[]): Promise<IConflictResult>;
    /**
     * Check against blackout dates
     */
    private static checkBlackoutDates;
    /**
     * Check against holidays
     */
    private static checkHolidays;
    /**
     * Check team member conflicts
     */
    private static checkTeamConflicts;
    /**
     * Get array of dates between start and end date
     */
    private static getDateRange;
    /**
     * Validate bulk operations
     */
    static validateBulkOperation(requests: ILeaveRequestCreate[], maxBulkSize?: number): IValidationResult;
    /**
     * Find duplicate requests in bulk operation
     */
    private static findDuplicateRequests;
}
//# sourceMappingURL=ValidationService.d.ts.map