var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
/**
 * Service class for Microsoft Graph operations
 */
var GraphService = /** @class */ (function () {
    function GraphService(context) {
        this.context = context;
    }
    /**
     * Initialize Graph client
     */
    GraphService.prototype.getGraphClient = function () {
        return __awaiter(this, void 0, void 0, function () {
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!!this.graphClient) return [3 /*break*/, 2];
                        _a = this;
                        return [4 /*yield*/, this.context.msGraphClientFactory.getClient('3')];
                    case 1:
                        _a.graphClient = _b.sent();
                        _b.label = 2;
                    case 2: return [2 /*return*/, this.graphClient];
                }
            });
        });
    };
    /**
     * Get current user profile with manager information
     */
    GraphService.prototype.getCurrentUserProfile = function () {
        return __awaiter(this, void 0, void 0, function () {
            var client, userResponse, manager, managerResponse, error_1, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 7, , 8]);
                        return [4 /*yield*/, this.getGraphClient()];
                    case 1:
                        client = _a.sent();
                        return [4 /*yield*/, client
                                .api('/me')
                                .select('id,displayName,mail,userPrincipalName,jobTitle,department')
                                .get()];
                    case 2:
                        userResponse = _a.sent();
                        manager = void 0;
                        _a.label = 3;
                    case 3:
                        _a.trys.push([3, 5, , 6]);
                        return [4 /*yield*/, client
                                .api('/me/manager')
                                .select('id,displayName,mail')
                                .get()];
                    case 4:
                        managerResponse = _a.sent();
                        manager = {
                            id: managerResponse.id,
                            displayName: managerResponse.displayName,
                            mail: managerResponse.mail
                        };
                        return [3 /*break*/, 6];
                    case 5:
                        error_1 = _a.sent();
                        console.warn('Manager information not available:', error_1);
                        manager = undefined;
                        return [3 /*break*/, 6];
                    case 6: return [2 /*return*/, {
                            id: userResponse.id,
                            displayName: userResponse.displayName,
                            mail: userResponse.mail,
                            userPrincipalName: userResponse.userPrincipalName,
                            jobTitle: userResponse.jobTitle,
                            department: userResponse.department,
                            manager: manager
                        }];
                    case 7:
                        error_2 = _a.sent();
                        console.error('Error fetching user profile:', error_2);
                        throw new Error('Failed to fetch user profile');
                    case 8: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Create calendar event for approved leave
     */
    GraphService.prototype.createCalendarEvent = function (event) {
        return __awaiter(this, void 0, void 0, function () {
            var client, response, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, this.getGraphClient()];
                    case 1:
                        client = _a.sent();
                        return [4 /*yield*/, client
                                .api('/me/events')
                                .post(event)];
                    case 2:
                        response = _a.sent();
                        return [2 /*return*/, response.id];
                    case 3:
                        error_3 = _a.sent();
                        console.error('Error creating calendar event:', error_3);
                        throw new Error('Failed to create calendar event');
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Update existing calendar event
     */
    GraphService.prototype.updateCalendarEvent = function (eventId, event) {
        return __awaiter(this, void 0, void 0, function () {
            var client, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, this.getGraphClient()];
                    case 1:
                        client = _a.sent();
                        return [4 /*yield*/, client
                                .api("/me/events/".concat(eventId))
                                .patch(event)];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_4 = _a.sent();
                        console.error('Error updating calendar event:', error_4);
                        throw new Error('Failed to update calendar event');
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Delete calendar event
     */
    GraphService.prototype.deleteCalendarEvent = function (eventId) {
        return __awaiter(this, void 0, void 0, function () {
            var client, error_5;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, this.getGraphClient()];
                    case 1:
                        client = _a.sent();
                        return [4 /*yield*/, client
                                .api("/me/events/".concat(eventId))
                                .delete()];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_5 = _a.sent();
                        console.error('Error deleting calendar event:', error_5);
                        throw new Error('Failed to delete calendar event');
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get team calendar events for conflict detection
     */
    GraphService.prototype.getTeamCalendarEvents = function (startDate, endDate, userIds) {
        return __awaiter(this, void 0, void 0, function () {
            var client, events, _i, userIds_1, userId, response, error_6, error_7;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 8, , 9]);
                        return [4 /*yield*/, this.getGraphClient()];
                    case 1:
                        client = _a.sent();
                        events = [];
                        _i = 0, userIds_1 = userIds;
                        _a.label = 2;
                    case 2:
                        if (!(_i < userIds_1.length)) return [3 /*break*/, 7];
                        userId = userIds_1[_i];
                        _a.label = 3;
                    case 3:
                        _a.trys.push([3, 5, , 6]);
                        return [4 /*yield*/, client
                                .api("/users/".concat(userId, "/calendarView"))
                                .query({
                                startDateTime: startDate.toISOString(),
                                endDateTime: endDate.toISOString()
                            })
                                .select('id,subject,start,end,isAllDay,showAs,categories')
                                .get()];
                    case 4:
                        response = _a.sent();
                        events.push.apply(events, response.value);
                        return [3 /*break*/, 6];
                    case 5:
                        error_6 = _a.sent();
                        console.warn("Could not fetch calendar for user ".concat(userId, ":"), error_6);
                        return [3 /*break*/, 6];
                    case 6:
                        _i++;
                        return [3 /*break*/, 2];
                    case 7: return [2 /*return*/, events];
                    case 8:
                        error_7 = _a.sent();
                        console.error('Error fetching team calendar events:', error_7);
                        throw new Error('Failed to fetch team calendar events');
                    case 9: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Send notification email
     */
    GraphService.prototype.sendNotificationEmail = function (to, subject, body, isHtml) {
        if (isHtml === void 0) { isHtml = false; }
        return __awaiter(this, void 0, void 0, function () {
            var client, message, error_8;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, this.getGraphClient()];
                    case 1:
                        client = _a.sent();
                        message = {
                            subject: subject,
                            body: {
                                contentType: isHtml ? 'html' : 'text',
                                content: body
                            },
                            toRecipients: to.map(function (email) { return ({
                                emailAddress: {
                                    address: email
                                }
                            }); })
                        };
                        return [4 /*yield*/, client
                                .api('/me/sendMail')
                                .post({
                                message: message,
                                saveToSentItems: true
                            })];
                    case 2:
                        _a.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        error_8 = _a.sent();
                        console.error('Error sending notification email:', error_8);
                        throw new Error('Failed to send notification email');
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Get user's direct reports (for managers)
     */
    GraphService.prototype.getDirectReports = function () {
        return __awaiter(this, void 0, void 0, function () {
            var client, response, error_9;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, this.getGraphClient()];
                    case 1:
                        client = _a.sent();
                        return [4 /*yield*/, client
                                .api('/me/directReports')
                                .select('id,displayName,mail,userPrincipalName,jobTitle,department')
                                .get()];
                    case 2:
                        response = _a.sent();
                        return [2 /*return*/, response.value.map(function (user) { return ({
                                id: user.id,
                                displayName: user.displayName,
                                mail: user.mail,
                                userPrincipalName: user.userPrincipalName,
                                jobTitle: user.jobTitle,
                                department: user.department
                            }); })];
                    case 3:
                        error_9 = _a.sent();
                        console.error('Error fetching direct reports:', error_9);
                        throw new Error('Failed to fetch direct reports');
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Check if user has manager permissions
     */
    GraphService.prototype.hasManagerPermissions = function () {
        return __awaiter(this, void 0, void 0, function () {
            var directReports, error_10;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, this.getDirectReports()];
                    case 1:
                        directReports = _a.sent();
                        return [2 /*return*/, directReports.length > 0];
                    case 2:
                        error_10 = _a.sent();
                        console.warn('Could not check manager permissions:', error_10);
                        return [2 /*return*/, false];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * Create leave request calendar event
     */
    GraphService.prototype.createLeaveCalendarEvent = function (leaveTypeName, startDate, endDate, isPartialDay, partialDayHours, comments) {
        return __awaiter(this, void 0, void 0, function () {
            var subject, timeZone, body, event;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        subject = "".concat(leaveTypeName, " - Out of Office");
                        timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                        body = "Leave Type: ".concat(leaveTypeName);
                        if (isPartialDay && partialDayHours) {
                            body += "\nPartial Day: ".concat(partialDayHours, " hours");
                        }
                        if (comments) {
                            body += "\nComments: ".concat(comments);
                        }
                        event = {
                            subject: subject,
                            start: {
                                dateTime: startDate.toISOString(),
                                timeZone: timeZone
                            },
                            end: {
                                dateTime: endDate.toISOString(),
                                timeZone: timeZone
                            },
                            isAllDay: !isPartialDay,
                            showAs: 'oof',
                            categories: ['Leave Request'],
                            body: {
                                contentType: 'text',
                                content: body
                            }
                        };
                        return [4 /*yield*/, this.createCalendarEvent(event)];
                    case 1: return [2 /*return*/, _a.sent()];
                }
            });
        });
    };
    return GraphService;
}());
export { GraphService };
//# sourceMappingURL=GraphService.js.map