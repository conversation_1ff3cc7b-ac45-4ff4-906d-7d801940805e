import { WebPartContext } from '@microsoft/sp-webpart-base';
import { ILeaveRequest, ILeaveType } from '../models';
/**
 * Interface for notification template data
 */
export interface INotificationTemplate {
    subject: string;
    body: string;
    isHtml: boolean;
}
/**
 * Service class for handling notifications
 */
export declare class NotificationService {
    private context;
    private graphService;
    constructor(context: WebPartContext);
    /**
     * Send leave request submission notification
     */
    sendSubmissionNotification(leaveRequest: ILeaveRequest, leaveType: ILeaveType): Promise<void>;
    /**
     * Send approval notification
     */
    sendApprovalNotification(leaveRequest: ILeaveRequest, leaveType: ILeaveType, isApproved: boolean, approverComments?: string): Promise<void>;
    /**
     * Send reminder notification for pending approvals
     */
    sendPendingApprovalReminder(leaveRequest: ILeaveRequest, leaveType: ILeaveType): Promise<void>;
    /**
     * Send leave balance warning notification
     */
    sendBalanceWarningNotification(userEmail: string, leaveTypeName: string, remainingDays: number, expirationDate: Date): Promise<void>;
    /**
     * Get submission notification template
     */
    private getSubmissionTemplate;
    /**
     * Get approval notification template
     */
    private getApprovalTemplate;
    /**
     * Get pending approval reminder template
     */
    private getPendingApprovalReminderTemplate;
    /**
     * Get balance warning notification template
     */
    private getBalanceWarningTemplate;
    /**
     * Create notification for Teams (if available)
     */
    sendTeamsNotification(message: string, title?: string): Promise<void>;
    /**
     * Batch send notifications for multiple requests
     */
    sendBatchNotifications(notifications: Array<{
        type: 'submission' | 'approval' | 'reminder' | 'balance-warning';
        data: any;
    }>): Promise<void>;
}
//# sourceMappingURL=NotificationService.d.ts.map