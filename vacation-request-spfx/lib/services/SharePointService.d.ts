import '@pnp/sp/webs';
import '@pnp/sp/lists';
import '@pnp/sp/items';
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { ILeaveRequest, ILeaveRequestCreate, ILeaveType, ILeaveBalance } from '../models';
/**
 * Service class for SharePoint operations
 */
export declare class SharePointService {
    private context;
    private sp;
    constructor(context: WebPartContext);
    /**
     * Get all active leave types
     */
    getLeaveTypes(): Promise<ILeaveType[]>;
    /**
     * Get leave type by ID
     */
    getLeaveTypeById(id: number): Promise<ILeaveType | null>;
    /**
     * Create a new leave request
     */
    createLeaveRequest(request: ILeaveRequestCreate): Promise<ILeaveRequest>;
    /**
     * Get leave request by ID
     */
    getLeaveRequestById(id: number): Promise<ILeaveRequest>;
    /**
     * Get current user's leave requests
     */
    getCurrentUserLeaveRequests(): Promise<ILeaveRequest[]>;
    /**
     * Get current user's leave balances
     */
    getCurrentUserLeaveBalances(): Promise<ILeaveBalance[]>;
    /**
     * Validate leave request against business rules
     */
    validateLeaveRequest(request: ILeaveRequestCreate): Promise<{
        isValid: boolean;
        errors: string[];
    }>;
}
//# sourceMappingURL=SharePointService.d.ts.map