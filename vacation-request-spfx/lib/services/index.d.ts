/**
 * Export all services
 */
export * from './SharePointService';
export * from './GraphService';
export * from './NotificationService';
export * from './ValidationService';
/**
 * Service factory for creating service instances
 */
import { WebPartContext } from '@microsoft/sp-webpart-base';
import { SharePointService } from './SharePointService';
import { GraphService } from './GraphService';
import { NotificationService } from './NotificationService';
export declare class ServiceFactory {
    /**
     * Create SharePoint service instance
     */
    static createSharePointService(context: WebPartContext): SharePointService;
    /**
     * Create Graph service instance
     */
    static createGraphService(context: WebPartContext): GraphService;
    /**
     * Create Notification service instance
     */
    static createNotificationService(context: WebPartContext): NotificationService;
}
/**
 * Service manager for coordinating multiple services
 */
export declare class ServiceManager {
    private sharePointService;
    private graphService;
    private notificationService;
    constructor(context: WebPartContext);
    /**
     * Get SharePoint service
     */
    getSharePointService(): SharePointService;
    /**
     * Get Graph service
     */
    getGraphService(): GraphService;
    /**
     * Get Notification service
     */
    getNotificationService(): NotificationService;
    /**
     * Submit leave request with full workflow
     */
    submitLeaveRequestWithWorkflow(request: any, sendNotifications?: boolean): Promise<any>;
    /**
     * Approve leave request with full workflow
     */
    approveLeaveRequestWithWorkflow(requestId: number, approverComments?: string, sendNotifications?: boolean): Promise<void>;
    /**
     * Reject leave request with full workflow
     */
    rejectLeaveRequestWithWorkflow(requestId: number, approverComments?: string, sendNotifications?: boolean): Promise<void>;
    /**
     * Cancel leave request with cleanup
     */
    cancelLeaveRequestWithCleanup(requestId: number, sendNotifications?: boolean): Promise<void>;
    /**
     * Get comprehensive user dashboard data
     */
    getUserDashboardData(userId?: string): Promise<any>;
}
//# sourceMappingURL=index.d.ts.map