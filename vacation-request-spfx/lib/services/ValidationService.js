var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
import { CommonUtils } from '../models';
/**
 * Service class for validation and business rules
 */
var ValidationService = /** @class */ (function () {
    function ValidationService() {
    }
    /**
     * Validate leave request against business rules
     */
    ValidationService.validateLeaveRequest = function (request, leaveType, leaveBalance, existingRequests) {
        var errors = [];
        var warnings = [];
        // Basic field validation
        this.validateBasicFields(request, errors);
        // Date validation
        this.validateDates(request, errors, warnings);
        // Leave type specific validation
        this.validateLeaveType(request, leaveType, errors, warnings);
        // Balance validation
        if (leaveBalance) {
            this.validateBalance(request, leaveBalance, errors, warnings);
        }
        // Overlap validation
        if (existingRequests) {
            this.validateOverlaps(request, existingRequests, errors, warnings);
        }
        return {
            isValid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    };
    /**
     * Validate basic required fields
     */
    ValidationService.validateBasicFields = function (request, errors) {
        if (!request.LeaveTypeId) {
            errors.push('Leave type is required');
        }
        if (!request.StartDate) {
            errors.push('Start date is required');
        }
        if (!request.EndDate) {
            errors.push('End date is required');
        }
        if (request.IsPartialDay && (!request.PartialDayHours || request.PartialDayHours <= 0)) {
            errors.push('Partial day hours must be specified for partial day requests');
        }
        if (request.PartialDayHours && (request.PartialDayHours < 0.5 || request.PartialDayHours > 8)) {
            errors.push('Partial day hours must be between 0.5 and 8 hours');
        }
    };
    /**
     * Validate dates
     */
    ValidationService.validateDates = function (request, errors, warnings) {
        if (!request.StartDate || !request.EndDate) {
            return; // Already handled in basic validation
        }
        // End date must be after or equal to start date
        if (request.EndDate < request.StartDate) {
            errors.push('End date must be after or equal to start date');
            return;
        }
        // Check for past dates (with some exceptions)
        var today = new Date();
        today.setHours(0, 0, 0, 0);
        if (request.StartDate < today) {
            errors.push('Cannot request leave for past dates');
        }
        // Warn about short notice (less than 2 business days)
        var businessDaysNotice = CommonUtils.calculateBusinessDays(today, request.StartDate);
        if (businessDaysNotice < 2 && businessDaysNotice >= 0) {
            warnings.push('Short notice: Consider providing more advance notice for leave requests');
        }
        // Warn about weekend dates
        if (CommonUtils.isWeekend(request.StartDate) || CommonUtils.isWeekend(request.EndDate)) {
            warnings.push('Leave request includes weekend dates');
        }
    };
    /**
     * Validate against leave type rules
     */
    ValidationService.validateLeaveType = function (request, leaveType, errors, warnings) {
        // Check if leave type is active
        if (!leaveType.IsActive) {
            errors.push("Leave type \"".concat(leaveType.Title, "\" is not currently available"));
            return;
        }
        // Check maximum days per request
        if (leaveType.MaxDaysPerRequest) {
            var requestedDays = CommonUtils.calculateBusinessDays(request.StartDate, request.EndDate);
            if (requestedDays > leaveType.MaxDaysPerRequest) {
                errors.push("Maximum ".concat(leaveType.MaxDaysPerRequest, " days allowed per request for ").concat(leaveType.Title, ". ") +
                    "You requested ".concat(requestedDays, " days."));
            }
        }
        // Check documentation requirement
        if (leaveType.RequiresDocumentation && !request.AttachmentURL) {
            errors.push("Documentation is required for ".concat(leaveType.Title, " requests"));
        }
        // Partial day validation for specific leave types
        if (request.IsPartialDay && leaveType.Title.toLowerCase().includes('maternity')) {
            warnings.push('Partial day requests are unusual for maternity/paternity leave');
        }
    };
    /**
     * Validate against leave balance
     */
    ValidationService.validateBalance = function (request, balance, errors, warnings) {
        var requestedDays = request.IsPartialDay && request.PartialDayHours ?
            request.PartialDayHours / 8 :
            CommonUtils.calculateBusinessDays(request.StartDate, request.EndDate);
        // Check if sufficient balance
        if (requestedDays > balance.RemainingDays) {
            errors.push("Insufficient leave balance. Requested: ".concat(requestedDays, " days, ") +
                "Available: ".concat(balance.RemainingDays, " days"));
        }
        // Warn if using significant portion of balance
        var usagePercentage = (requestedDays / balance.TotalAllowance) * 100;
        if (usagePercentage > 50) {
            warnings.push("This request will use ".concat(usagePercentage.toFixed(1), "% of your annual ").concat(balance.LeaveType.Title, " allowance"));
        }
        // Check expiration
        var today = new Date();
        var daysUntilExpiry = Math.ceil((balance.ExpirationDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        if (daysUntilExpiry <= 30 && daysUntilExpiry > 0) {
            warnings.push("Your ".concat(balance.LeaveType.Title, " balance expires in ").concat(daysUntilExpiry, " days. ") +
                "Consider using remaining days before expiration.");
        }
    };
    /**
     * Validate for overlapping requests
     */
    ValidationService.validateOverlaps = function (request, existingRequests, errors, warnings) {
        var overlappingRequests = existingRequests.filter(function (existing) {
            // Skip cancelled or rejected requests
            if (existing.ApprovalStatus === 'Cancelled' || existing.ApprovalStatus === 'Rejected') {
                return false;
            }
            // Check for date overlap
            return ((request.StartDate <= existing.EndDate && request.EndDate >= existing.StartDate));
        });
        if (overlappingRequests.length > 0) {
            errors.push("You have overlapping leave requests. Please check your existing requests and modify dates if needed.");
        }
    };
    /**
     * Detect conflicts with team members and blackout dates
     */
    ValidationService.detectConflicts = function (request, teamMembers, blackoutDates, holidays) {
        return __awaiter(this, void 0, void 0, function () {
            var conflicts;
            return __generator(this, function (_a) {
                conflicts = [];
                // Check blackout dates
                this.checkBlackoutDates(request, blackoutDates, conflicts);
                // Check holidays
                this.checkHolidays(request, holidays, conflicts);
                // Check team member conflicts
                this.checkTeamConflicts(request, teamMembers, conflicts);
                return [2 /*return*/, {
                        hasConflicts: conflicts.some(function (c) { return c.severity === 'error'; }),
                        conflicts: conflicts
                    }];
            });
        });
    };
    /**
     * Check against blackout dates
     */
    ValidationService.checkBlackoutDates = function (request, blackoutDates, conflicts) {
        var requestDates = this.getDateRange(request.StartDate, request.EndDate);
        var conflictingDates = requestDates.filter(function (date) {
            return blackoutDates.some(function (blackout) {
                return date.toDateString() === blackout.toDateString();
            });
        });
        if (conflictingDates.length > 0) {
            conflicts.push({
                type: 'blackout-date',
                severity: 'error',
                message: "Your request conflicts with company blackout dates: ".concat(conflictingDates.map(function (d) { return d.toLocaleDateString(); }).join(', ')),
                details: { conflictingDates: conflictingDates }
            });
        }
    };
    /**
     * Check against holidays
     */
    ValidationService.checkHolidays = function (request, holidays, conflicts) {
        var requestDates = this.getDateRange(request.StartDate, request.EndDate);
        var holidayConflicts = requestDates.filter(function (date) {
            return holidays.some(function (holiday) {
                return date.toDateString() === holiday.toDateString();
            });
        });
        if (holidayConflicts.length > 0) {
            conflicts.push({
                type: 'holiday',
                severity: 'warning',
                message: "Your request includes company holidays: ".concat(holidayConflicts.map(function (d) { return d.toLocaleDateString(); }).join(', '), ". These days may not count against your leave balance."),
                details: { holidayConflicts: holidayConflicts }
            });
        }
    };
    /**
     * Check team member conflicts
     */
    ValidationService.checkTeamConflicts = function (request, teamMembers, conflicts) {
        var conflictingMembers = teamMembers.filter(function (member) {
            var _a;
            return (_a = member.leaveRequests) === null || _a === void 0 ? void 0 : _a.some(function (leave) {
                return leave.ApprovalStatus === 'Approved' &&
                    request.StartDate <= leave.EndDate &&
                    request.EndDate >= leave.StartDate;
            });
        });
        if (conflictingMembers.length > 0) {
            var memberNames = conflictingMembers.map(function (m) { return m.displayName; }).join(', ');
            conflicts.push({
                type: 'team-member',
                severity: 'warning',
                message: "Team members with overlapping leave: ".concat(memberNames, ". ") +
                    "Please coordinate with your team to ensure adequate coverage.",
                details: { conflictingMembers: conflictingMembers }
            });
        }
    };
    /**
     * Get array of dates between start and end date
     */
    ValidationService.getDateRange = function (startDate, endDate) {
        var dates = [];
        var current = new Date(startDate.getTime());
        while (current <= endDate) {
            dates.push(new Date(current.getTime()));
            current.setDate(current.getDate() + 1);
        }
        return dates;
    };
    /**
     * Validate bulk operations
     */
    ValidationService.validateBulkOperation = function (requests, maxBulkSize) {
        if (maxBulkSize === void 0) { maxBulkSize = 10; }
        var errors = [];
        var warnings = [];
        if (requests.length === 0) {
            errors.push('No requests provided for bulk operation');
        }
        if (requests.length > maxBulkSize) {
            errors.push("Bulk operation limited to ".concat(maxBulkSize, " requests. Provided: ").concat(requests.length));
        }
        // Check for duplicate requests
        var duplicates = this.findDuplicateRequests(requests);
        if (duplicates.length > 0) {
            warnings.push("Found ".concat(duplicates.length, " potential duplicate requests"));
        }
        return {
            isValid: errors.length === 0,
            errors: errors,
            warnings: warnings
        };
    };
    /**
     * Find duplicate requests in bulk operation
     */
    ValidationService.findDuplicateRequests = function (requests) {
        var duplicateIndices = [];
        for (var i = 0; i < requests.length; i++) {
            for (var j = i + 1; j < requests.length; j++) {
                if (requests[i].LeaveTypeId === requests[j].LeaveTypeId &&
                    requests[i].StartDate.getTime() === requests[j].StartDate.getTime() &&
                    requests[i].EndDate.getTime() === requests[j].EndDate.getTime()) {
                    duplicateIndices.push(j);
                }
            }
        }
        return duplicateIndices;
    };
    return ValidationService;
}());
export { ValidationService };
//# sourceMappingURL=ValidationService.js.map