import { WebPartContext } from '@microsoft/sp-webpart-base';
/**
 * Interface for calendar event
 */
export interface ICalendarEvent {
    id?: string;
    subject: string;
    start: {
        dateTime: string;
        timeZone: string;
    };
    end: {
        dateTime: string;
        timeZone: string;
    };
    isAllDay: boolean;
    showAs: 'free' | 'tentative' | 'busy' | 'oof' | 'workingElsewhere';
    categories: string[];
    body?: {
        contentType: 'text' | 'html';
        content: string;
    };
}
/**
 * Interface for user profile information
 */
export interface IUserProfile {
    id: string;
    displayName: string;
    mail: string;
    userPrincipalName: string;
    jobTitle?: string;
    department?: string;
    manager?: {
        id: string;
        displayName: string;
        mail: string;
    };
}
/**
 * Service class for Microsoft Graph operations
 */
export declare class GraphService {
    private context;
    private graphClient;
    constructor(context: WebPartContext);
    /**
     * Initialize Graph client
     */
    private getGraphClient;
    /**
     * Get current user profile with manager information
     */
    getCurrentUserProfile(): Promise<IUserProfile>;
    /**
     * Create calendar event for approved leave
     */
    createCalendarEvent(event: ICalendarEvent): Promise<string>;
    /**
     * Update existing calendar event
     */
    updateCalendarEvent(eventId: string, event: Partial<ICalendarEvent>): Promise<void>;
    /**
     * Delete calendar event
     */
    deleteCalendarEvent(eventId: string): Promise<void>;
    /**
     * Get team calendar events for conflict detection
     */
    getTeamCalendarEvents(startDate: Date, endDate: Date, userIds: string[]): Promise<ICalendarEvent[]>;
    /**
     * Send notification email
     */
    sendNotificationEmail(to: string[], subject: string, body: string, isHtml?: boolean): Promise<void>;
    /**
     * Get user's direct reports (for managers)
     */
    getDirectReports(): Promise<IUserProfile[]>;
    /**
     * Check if user has manager permissions
     */
    hasManagerPermissions(): Promise<boolean>;
    /**
     * Create leave request calendar event
     */
    createLeaveCalendarEvent(leaveTypeName: string, startDate: Date, endDate: Date, isPartialDay: boolean, partialDayHours?: number, comments?: string): Promise<string>;
}
//# sourceMappingURL=GraphService.d.ts.map