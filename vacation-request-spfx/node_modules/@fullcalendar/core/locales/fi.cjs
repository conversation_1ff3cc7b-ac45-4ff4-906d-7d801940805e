'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var l28 = {
    code: 'fi',
    week: {
        dow: 1,
        doy: 4, // The week that contains Jan 4th is the first week of the year.
    },
    buttonText: {
        prev: '<PERSON><PERSON><PERSON>',
        next: '<PERSON><PERSON><PERSON>',
        today: '<PERSON>ä<PERSON><PERSON>än',
        year: 'Vuosi',
        month: '<PERSON>uka<PERSON><PERSON>',
        week: 'Viik<PERSON>',
        day: '<PERSON><PERSON><PERSON><PERSON>',
        list: 'Tapahtumat',
    },
    weekText: 'Vk',
    allDayText: '<PERSON><PERSON> päivä',
    moreLinkText: 'lisää',
    noEventsText: 'Ei näytettäviä tapahtumia',
};

exports["default"] = l28;
