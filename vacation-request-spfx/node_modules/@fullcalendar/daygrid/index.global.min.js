/*!
FullCalendar Day Grid Plugin v6.1.18
Docs & License: https://fullcalendar.io/docs/month-view
(c) 2024 Adam Shaw
*/
FullCalendar.DayGrid=function(e,t,n,s){"use strict";class i extends n.DateComponent{constructor(){super(...arguments),this.headerElRef=s.createRef()}renderSimpleLayout(e,t){let{props:i,context:r}=this,a=[],l=n.getStickyHeaderDates(r.options);return e&&a.push({type:"header",key:"header",isSticky:l,chunk:{elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}}),a.push({type:"body",key:"body",liquid:!0,chunk:{content:t}}),s.createElement(n.ViewContainer,{elClasses:["fc-daygrid"],viewSpec:r.viewSpec},s.createElement(n.SimpleScrollGrid,{liquid:!i.isHeightAuto&&!i.forPrint,collapsibleWidth:i.forPrint,cols:[],sections:a}))}renderHScrollLayout(e,t,i,r){let a=this.context.pluginHooks.scrollGridImpl;if(!a)throw new Error("No ScrollGrid implementation");let{props:l,context:o}=this,d=!l.forPrint&&n.getStickyHeaderDates(o.options),c=!l.forPrint&&n.getStickyFooterScrollbar(o.options),g=[];return e&&g.push({type:"header",key:"header",isSticky:d,chunks:[{key:"main",elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}]}),g.push({type:"body",key:"body",liquid:!0,chunks:[{key:"main",content:t}]}),c&&g.push({type:"footer",key:"footer",isSticky:!0,chunks:[{key:"main",content:n.renderScrollShim}]}),s.createElement(n.ViewContainer,{elClasses:["fc-daygrid"],viewSpec:o.viewSpec},s.createElement(a,{liquid:!l.isHeightAuto&&!l.forPrint,forPrint:l.forPrint,collapsibleWidth:l.forPrint,colGroups:[{cols:[{span:i,minWidth:r}]}],sections:g}))}}function r(e,t){let n=[];for(let e=0;e<t;e+=1)n[e]=[];for(let t of e)n[t.row].push(t);return n}function a(e,t){let n=[];for(let e=0;e<t;e+=1)n[e]=[];for(let t of e)n[t.firstCol].push(t);return n}function l(e,t){let n=[];if(e){for(let s=0;s<t;s+=1)n[s]={affectedInstances:e.affectedInstances,isEvent:e.isEvent,segs:[]};for(let t of e.segs)n[t.row].segs.push(t)}else for(let e=0;e<t;e+=1)n[e]=null;return n}const o=n.createFormatter({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"narrow"});function d(e){let{display:t}=e.eventRange.ui;return"list-item"===t||"auto"===t&&!e.eventRange.def.allDay&&e.firstCol===e.lastCol&&e.isStart&&e.isEnd}class c extends n.BaseComponent{render(){let{props:e}=this;return s.createElement(n.StandardEvent,Object.assign({},e,{elClasses:["fc-daygrid-event","fc-daygrid-block-event","fc-h-event"],defaultTimeFormat:o,defaultDisplayEventEnd:e.defaultDisplayEventEnd,disableResizing:!e.seg.eventRange.def.allDay}))}}class g extends n.BaseComponent{render(){let{props:e,context:t}=this,{options:i}=t,{seg:r}=e,a=i.eventTimeFormat||o,l=n.buildSegTimeText(r,a,t,!0,e.defaultDisplayEventEnd);return s.createElement(n.EventContainer,Object.assign({},e,{elTag:"a",elClasses:["fc-daygrid-event","fc-daygrid-dot-event"],elAttrs:n.getSegAnchorAttrs(e.seg,t),defaultGenerator:f,timeText:l,isResizing:!1,isDateSelecting:!1}))}}function f(e){return s.createElement(s.Fragment,null,s.createElement("div",{className:"fc-daygrid-event-dot",style:{borderColor:e.borderColor||e.backgroundColor}}),e.timeText&&s.createElement("div",{className:"fc-event-time"},e.timeText),s.createElement("div",{className:"fc-event-title"},e.event.title||s.createElement(s.Fragment,null," ")))}class h extends n.BaseComponent{constructor(){super(...arguments),this.compileSegs=n.memoize(u)}render(){let{props:e}=this,{allSegs:t,invisibleSegs:i}=this.compileSegs(e.singlePlacements);return s.createElement(n.MoreLinkContainer,{elClasses:["fc-daygrid-more-link"],dateProfile:e.dateProfile,todayRange:e.todayRange,allDayDate:e.allDayDate,moreCnt:e.moreCnt,allSegs:t,hiddenSegs:i,alignmentElRef:e.alignmentElRef,alignGridTop:e.alignGridTop,extraDateSpan:e.extraDateSpan,popoverContent:()=>{let i=(e.eventDrag?e.eventDrag.affectedInstances:null)||(e.eventResize?e.eventResize.affectedInstances:null)||{};return s.createElement(s.Fragment,null,t.map(t=>{let r=t.eventRange.instance.instanceId;return s.createElement("div",{className:"fc-daygrid-event-harness",key:r,style:{visibility:i[r]?"hidden":""}},d(t)?s.createElement(g,Object.assign({seg:t,isDragging:!1,isSelected:r===e.eventSelection,defaultDisplayEventEnd:!1},n.getSegMeta(t,e.todayRange))):s.createElement(c,Object.assign({seg:t,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:r===e.eventSelection,defaultDisplayEventEnd:!1},n.getSegMeta(t,e.todayRange))))}))}})}}function u(e){let t=[],n=[];for(let s of e)t.push(s.seg),s.isVisible||n.push(s.seg);return{allSegs:t,invisibleSegs:n}}const p=n.createFormatter({week:"narrow"});class m extends n.DateComponent{constructor(){super(...arguments),this.rootElRef=s.createRef(),this.state={dayNumberId:n.getUniqueDomId()},this.handleRootEl=e=>{n.setRef(this.rootElRef,e),n.setRef(this.props.elRef,e)}}render(){let{context:e,props:t,state:i,rootElRef:r}=this,{options:a,dateEnv:l}=e,{date:o,dateProfile:d}=t;const c=t.showDayNumber&&function(e,t,s){const{start:i,end:r}=t,a=n.addMs(r,-1),l=s.getYear(i),o=s.getMonth(i),d=s.getYear(a),c=s.getMonth(a);return!(l===d&&o===c)&&Boolean(e.valueOf()===i.valueOf()||1===s.getDay(e)&&e.valueOf()<r.valueOf())}(o,d.currentRange,l);return s.createElement(n.DayCellContainer,{elTag:"td",elRef:this.handleRootEl,elClasses:["fc-daygrid-day",...t.extraClassNames||[]],elAttrs:Object.assign(Object.assign(Object.assign({},t.extraDataAttrs),t.showDayNumber?{"aria-labelledby":i.dayNumberId}:{}),{role:"gridcell"}),defaultGenerator:y,date:o,dateProfile:d,todayRange:t.todayRange,showDayNumber:t.showDayNumber,isMonthStart:c,extraRenderProps:t.extraRenderProps},(l,d)=>s.createElement("div",{ref:t.innerElRef,className:"fc-daygrid-day-frame fc-scrollgrid-sync-inner",style:{minHeight:t.minHeight}},t.showWeekNumber&&s.createElement(n.WeekNumberContainer,{elTag:"a",elClasses:["fc-daygrid-week-number"],elAttrs:n.buildNavLinkAttrs(e,o,"week"),date:o,defaultFormat:p}),!d.isDisabled&&(t.showDayNumber||n.hasCustomDayCellContent(a)||t.forceDayTop)?s.createElement("div",{className:"fc-daygrid-day-top"},s.createElement(l,{elTag:"a",elClasses:["fc-daygrid-day-number",c&&"fc-daygrid-month-start"],elAttrs:Object.assign(Object.assign({},n.buildNavLinkAttrs(e,o)),{id:i.dayNumberId})})):t.showDayNumber?s.createElement("div",{className:"fc-daygrid-day-top",style:{visibility:"hidden"}},s.createElement("a",{className:"fc-daygrid-day-number"}," ")):void 0,s.createElement("div",{className:"fc-daygrid-day-events",ref:t.fgContentElRef},t.fgContent,s.createElement("div",{className:"fc-daygrid-day-bottom",style:{marginTop:t.moreMarginTop}},s.createElement(h,{allDayDate:o,singlePlacements:t.singlePlacements,moreCnt:t.moreCnt,alignmentElRef:r,alignGridTop:!t.showDayNumber,extraDateSpan:t.extraDateSpan,dateProfile:t.dateProfile,eventSelection:t.eventSelection,eventDrag:t.eventDrag,eventResize:t.eventResize,todayRange:t.todayRange}))),s.createElement("div",{className:"fc-daygrid-day-bg"},t.bgContent)))}}function y(e){return e.dayNumberText||s.createElement(s.Fragment,null," ")}function v(e){return e.eventRange.instance.instanceId+":"+e.firstCol}function b(e){return v(e)+":"+e.lastCol}function R(e,t,n,s,i,r,a){let l=new S(t=>{let n=e[t.index].eventRange.instance.instanceId+":"+t.span.start+":"+(t.span.end-1);return i[n]||1});l.allowReslicing=!0,l.strictOrder=s,!0===t||!0===n?(l.maxCoord=r,l.hiddenConsumes=!0):"number"==typeof t?l.maxStackCnt=t:"number"==typeof n&&(l.maxStackCnt=n,l.hiddenConsumes=!0);let o=[],d=[];for(let t=0;t<e.length;t+=1){let n=e[t],s=b(n);null!=i[s]?o.push({index:t,span:{start:n.firstCol,end:n.lastCol+1}}):d.push(n)}let c=l.addSegs(o),g=l.toRects(),{singleColPlacements:f,multiColPlacements:h,leftoverMargins:u}=function(e,t,n){let s=function(e,t){let n=[];for(let e=0;e<t;e+=1)n.push([]);for(let t of e)for(let e=t.span.start;e<t.span.end;e+=1)n[e].push(t);return n}(e,n.length),i=[],r=[],a=[];for(let e=0;e<n.length;e+=1){let l=s[e],o=[],d=0,c=0;for(let s of l){let i=t[s.index];o.push({seg:E(i,e,e+1,n),isVisible:!0,isAbsolute:!1,absoluteTop:s.levelCoord,marginTop:s.levelCoord-d}),d=s.levelCoord+s.thickness}let g=[];d=0,c=0;for(let s of l){let i=t[s.index],r=s.span.end-s.span.start>1,a=s.span.start===e;c+=s.levelCoord-d,d=s.levelCoord+s.thickness,r?(c+=s.thickness,a&&g.push({seg:E(i,s.span.start,s.span.end,n),isVisible:!0,isAbsolute:!0,absoluteTop:s.levelCoord,marginTop:0})):a&&(g.push({seg:E(i,s.span.start,s.span.end,n),isVisible:!0,isAbsolute:!1,absoluteTop:s.levelCoord,marginTop:c}),c=0)}i.push(o),r.push(g),a.push(c)}return{singleColPlacements:i,multiColPlacements:r,leftoverMargins:a}}(g,e,a),p=[],m=[];for(let e of d){h[e.firstCol].push({seg:e,isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(let t=e.firstCol;t<=e.lastCol;t+=1)f[t].push({seg:E(e,t,t+1,a),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(let e=0;e<a.length;e+=1)p.push(0);for(let t of c){let n=e[t.index],s=t.span;h[s.start].push({seg:E(n,s.start,s.end,a),isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(let e=s.start;e<s.end;e+=1)p[e]+=1,f[e].push({seg:E(n,e,e+1,a),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(let e=0;e<a.length;e+=1)m.push(u[e]);return{singleColPlacements:f,multiColPlacements:h,moreCnts:p,moreMarginTops:m}}function E(e,t,s,i){if(e.firstCol===t&&e.lastCol===s-1)return e;let r=e.eventRange,a=r.range,l=n.intersectRanges(a,{start:i[t].date,end:n.addDays(i[s-1].date,1)});return Object.assign(Object.assign({},e),{firstCol:t,lastCol:s-1,eventRange:{def:r.def,ui:Object.assign(Object.assign({},r.ui),{durationEditable:!1}),instance:r.instance,range:l},isStart:e.isStart&&l.start.valueOf()===a.start.valueOf(),isEnd:e.isEnd&&l.end.valueOf()===a.end.valueOf()})}class S extends n.SegHierarchy{constructor(){super(...arguments),this.hiddenConsumes=!1,this.forceHidden={}}addSegs(e){const t=super.addSegs(e),{entriesByLevel:s}=this,i=e=>!this.forceHidden[n.buildEntryKey(e)];for(let e=0;e<s.length;e+=1)s[e]=s[e].filter(i);return t}handleInvalidInsertion(e,t,s){const{entriesByLevel:i,forceHidden:r}=this,{touchingEntry:a,touchingLevel:l,touchingLateral:o}=e;if(this.hiddenConsumes&&a){const e=n.buildEntryKey(a);if(!r[e])if(this.allowReslicing){const e=Object.assign(Object.assign({},a),{span:n.intersectSpans(a.span,t.span)});r[n.buildEntryKey(e)]=!0,i[l][o]=e,s.push(e),this.splitEntry(a,t,s)}else r[e]=!0,s.push(a)}super.handleInvalidInsertion(e,t,s)}}class x extends n.DateComponent{constructor(){super(...arguments),this.cellElRefs=new n.RefMap,this.frameElRefs=new n.RefMap,this.fgElRefs=new n.RefMap,this.segHarnessRefs=new n.RefMap,this.rootElRef=s.createRef(),this.state={framePositions:null,maxContentHeight:null,segHeights:{}},this.handleResize=e=>{e&&this.updateSizing(!0)}}render(){let{props:e,state:t,context:i}=this,{options:r}=i,l=e.cells.length,o=a(e.businessHourSegs,l),d=a(e.bgEventSegs,l),c=a(this.getHighlightSegs(),l),g=a(this.getMirrorSegs(),l),{singleColPlacements:f,multiColPlacements:h,moreCnts:u,moreMarginTops:p}=R(n.sortEventSegs(e.fgEventSegs,r.eventOrder),e.dayMaxEvents,e.dayMaxEventRows,r.eventOrderStrict,t.segHeights,t.maxContentHeight,e.cells),y=e.eventDrag&&e.eventDrag.affectedInstances||e.eventResize&&e.eventResize.affectedInstances||{};return s.createElement("tr",{ref:this.rootElRef,role:"row"},e.renderIntro&&e.renderIntro(),e.cells.map((t,n)=>{let i=this.renderFgSegs(n,e.forPrint?f[n]:h[n],e.todayRange,y),r=this.renderFgSegs(n,function(e,t){if(!e.length)return[];let n=function(e){let t={};for(let n of e)for(let e of n)t[e.seg.eventRange.instance.instanceId]=e.absoluteTop;return t}(t);return e.map(e=>({seg:e,isVisible:!0,isAbsolute:!0,absoluteTop:n[e.eventRange.instance.instanceId],marginTop:0}))}(g[n],h),e.todayRange,{},Boolean(e.eventDrag),Boolean(e.eventResize),!1);return s.createElement(m,{key:t.key,elRef:this.cellElRefs.createRef(t.key),innerElRef:this.frameElRefs.createRef(t.key),dateProfile:e.dateProfile,date:t.date,showDayNumber:e.showDayNumbers,showWeekNumber:e.showWeekNumbers&&0===n,forceDayTop:e.showWeekNumbers,todayRange:e.todayRange,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,extraRenderProps:t.extraRenderProps,extraDataAttrs:t.extraDataAttrs,extraClassNames:t.extraClassNames,extraDateSpan:t.extraDateSpan,moreCnt:u[n],moreMarginTop:p[n],singlePlacements:f[n],fgContentElRef:this.fgElRefs.createRef(t.key),fgContent:s.createElement(s.Fragment,null,s.createElement(s.Fragment,null,i),s.createElement(s.Fragment,null,r)),bgContent:s.createElement(s.Fragment,null,this.renderFillSegs(c[n],"highlight"),this.renderFillSegs(o[n],"non-business"),this.renderFillSegs(d[n],"bg-event")),minHeight:e.cellMinHeight})}))}componentDidMount(){this.updateSizing(!0),this.context.addResizeHandler(this.handleResize)}componentDidUpdate(e,t){let s=this.props;this.updateSizing(!n.isPropsEqual(e,s))}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}getHighlightSegs(){let{props:e}=this;return e.eventDrag&&e.eventDrag.segs.length?e.eventDrag.segs:e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:e.dateSelectionSegs}getMirrorSegs(){let{props:e}=this;return e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:[]}renderFgSegs(e,t,i,r,a,l,o){let{context:f}=this,{eventSelection:h}=this.props,{framePositions:u}=this.state,p=1===this.props.cells.length,m=a||l||o,y=[];if(u)for(let e of t){let{seg:t}=e,{instanceId:R}=t.eventRange.instance,E=e.isVisible&&!r[R],S=e.isAbsolute,x="",C="";S&&(f.isRtl?(C=0,x=u.lefts[t.lastCol]-u.lefts[t.firstCol]):(x=0,C=u.rights[t.firstCol]-u.rights[t.lastCol])),y.push(s.createElement("div",{className:"fc-daygrid-event-harness"+(S?" fc-daygrid-event-harness-abs":""),key:v(t),ref:m?null:this.segHarnessRefs.createRef(b(t)),style:{visibility:E?"":"hidden",marginTop:S?"":e.marginTop,top:S?e.absoluteTop:"",left:x,right:C}},d(t)?s.createElement(g,Object.assign({seg:t,isDragging:a,isSelected:R===h,defaultDisplayEventEnd:p},n.getSegMeta(t,i))):s.createElement(c,Object.assign({seg:t,isDragging:a,isResizing:l,isDateSelecting:o,isSelected:R===h,defaultDisplayEventEnd:p},n.getSegMeta(t,i)))))}return y}renderFillSegs(e,t){let{isRtl:i}=this.context,{todayRange:r}=this.props,{framePositions:a}=this.state,l=[];if(a)for(let o of e){let e=i?{right:0,left:a.lefts[o.lastCol]-a.lefts[o.firstCol]}:{left:0,right:a.rights[o.firstCol]-a.rights[o.lastCol]};l.push(s.createElement("div",{key:n.buildEventRangeKey(o.eventRange),className:"fc-daygrid-bg-harness",style:e},"bg-event"===t?s.createElement(n.BgEvent,Object.assign({seg:o},n.getSegMeta(o,r))):n.renderFill(t)))}return s.createElement(s.Fragment,{},...l)}updateSizing(e){let{props:t,state:s,frameElRefs:i}=this;if(!t.forPrint&&null!==t.clientWidth){if(e){let e=t.cells.map(e=>i.currentMap[e.key]);if(e.length){let t=this.rootElRef.current,i=new n.PositionCache(t,e,!0,!1);s.framePositions&&s.framePositions.similarTo(i)||this.setState({framePositions:new n.PositionCache(t,e,!0,!1)})}}const r=this.state.segHeights,a=this.querySegHeights(),l=!0===t.dayMaxEvents||!0===t.dayMaxEventRows;this.safeSetState({segHeights:Object.assign(Object.assign({},r),a),maxContentHeight:l?this.computeMaxContentHeight():null})}}querySegHeights(){let e=this.segHarnessRefs.currentMap,t={};for(let n in e){let s=Math.round(e[n].getBoundingClientRect().height);t[n]=Math.max(t[n]||0,s)}return t}computeMaxContentHeight(){let e=this.props.cells[0].key,t=this.cellElRefs.currentMap[e],n=this.fgElRefs.currentMap[e];return t.getBoundingClientRect().bottom-n.getBoundingClientRect().top}getCellEls(){let e=this.cellElRefs.currentMap;return this.props.cells.map(t=>e[t.key])}}x.addStateEquality({segHeights:n.isPropsEqual});class C extends n.DateComponent{constructor(){super(...arguments),this.splitBusinessHourSegs=n.memoize(r),this.splitBgEventSegs=n.memoize(w),this.splitFgEventSegs=n.memoize(r),this.splitDateSelectionSegs=n.memoize(r),this.splitEventDrag=n.memoize(l),this.splitEventResize=n.memoize(l),this.rowRefs=new n.RefMap}render(){let{props:e,context:t}=this,i=e.cells.length,r=this.splitBusinessHourSegs(e.businessHourSegs,i),a=this.splitBgEventSegs(e.bgEventSegs,i),l=this.splitFgEventSegs(e.fgEventSegs,i),o=this.splitDateSelectionSegs(e.dateSelectionSegs,i),d=this.splitEventDrag(e.eventDrag,i),c=this.splitEventResize(e.eventResize,i),g=i>=7&&e.clientWidth?e.clientWidth/t.options.aspectRatio/6:null;return s.createElement(n.NowTimer,{unit:"day"},(t,n)=>s.createElement(s.Fragment,null,e.cells.map((t,f)=>s.createElement(x,{ref:this.rowRefs.createRef(f),key:t.length?t[0].date.toISOString():f,showDayNumbers:i>1,showWeekNumbers:e.showWeekNumbers,todayRange:n,dateProfile:e.dateProfile,cells:t,renderIntro:e.renderRowIntro,businessHourSegs:r[f],eventSelection:e.eventSelection,bgEventSegs:a[f],fgEventSegs:l[f],dateSelectionSegs:o[f],eventDrag:d[f],eventResize:c[f],dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,clientWidth:e.clientWidth,clientHeight:e.clientHeight,cellMinHeight:g,forPrint:e.forPrint}))))}componentDidMount(){this.registerInteractiveComponent()}componentDidUpdate(){this.registerInteractiveComponent()}registerInteractiveComponent(){if(!this.rootEl){const e=this.rowRefs.currentMap[0].getCellEls()[0],t=e?e.closest(".fc-daygrid-body"):null;t&&(this.rootEl=t,this.context.registerInteractiveComponent(this,{el:t,isHitComboAllowed:this.props.isHitComboAllowed}))}}componentWillUnmount(){this.rootEl&&(this.context.unregisterInteractiveComponent(this),this.rootEl=null)}prepareHits(){this.rowPositions=new n.PositionCache(this.rootEl,this.rowRefs.collect().map(e=>e.getCellEls()[0]),!1,!0),this.colPositions=new n.PositionCache(this.rootEl,this.rowRefs.currentMap[0].getCellEls(),!0,!1)}queryHit(e,t){let{colPositions:n,rowPositions:s}=this,i=n.leftToIndex(e),r=s.topToIndex(t);if(null!=r&&null!=i){let e=this.props.cells[r][i];return{dateProfile:this.props.dateProfile,dateSpan:Object.assign({range:this.getCellRange(r,i),allDay:!0},e.extraDateSpan),dayEl:this.getCellEl(r,i),rect:{left:n.lefts[i],right:n.rights[i],top:s.tops[r],bottom:s.bottoms[r]},layer:0}}return null}getCellEl(e,t){return this.rowRefs.currentMap[e].getCellEls()[t]}getCellRange(e,t){let s=this.props.cells[e][t].date;return{start:s,end:n.addDays(s,1)}}}function w(e,t){return r(e.filter(D),t)}function D(e){return e.eventRange.def.allDay}class k extends n.DateComponent{constructor(){super(...arguments),this.elRef=s.createRef(),this.needsScrollReset=!1}render(){let{props:e}=this,{dayMaxEventRows:t,dayMaxEvents:n,expandRows:i}=e,r=!0===n||!0===t;r&&!i&&(r=!1,t=null,n=null);let a=["fc-daygrid-body",r?"fc-daygrid-body-balanced":"fc-daygrid-body-unbalanced",i?"":"fc-daygrid-body-natural"];return s.createElement("div",{ref:this.elRef,className:a.join(" "),style:{width:e.clientWidth,minWidth:e.tableMinWidth}},s.createElement("table",{role:"presentation",className:"fc-scrollgrid-sync-table",style:{width:e.clientWidth,minWidth:e.tableMinWidth,height:i?e.clientHeight:""}},e.colGroupNode,s.createElement("tbody",{role:"presentation"},s.createElement(C,{dateProfile:e.dateProfile,cells:e.cells,renderRowIntro:e.renderRowIntro,showWeekNumbers:e.showWeekNumbers,clientWidth:e.clientWidth,clientHeight:e.clientHeight,businessHourSegs:e.businessHourSegs,bgEventSegs:e.bgEventSegs,fgEventSegs:e.fgEventSegs,dateSelectionSegs:e.dateSelectionSegs,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,dayMaxEvents:n,dayMaxEventRows:t,forPrint:e.forPrint,isHitComboAllowed:e.isHitComboAllowed}))))}componentDidMount(){this.requestScrollReset()}componentDidUpdate(e){e.dateProfile!==this.props.dateProfile?this.requestScrollReset():this.flushScrollReset()}requestScrollReset(){this.needsScrollReset=!0,this.flushScrollReset()}flushScrollReset(){if(this.needsScrollReset&&this.props.clientWidth){const e=function(e,t){let s;t.currentRangeUnit.match(/year|month/)&&(s=e.querySelector(`[data-date="${n.formatIsoMonthStr(t.currentDate)}-01"]`));s||(s=e.querySelector(`[data-date="${n.formatDayString(t.currentDate)}"]`));return s}(this.elRef.current,this.props.dateProfile);if(e){const t=e.closest(".fc-daygrid-body"),n=t.closest(".fc-scroller"),s=e.getBoundingClientRect().top-t.getBoundingClientRect().top;n.scrollTop=s?s+1:0}this.needsScrollReset=!1}}}class P extends n.Slicer{constructor(){super(...arguments),this.forceDayIfListItem=!0}sliceRange(e,t){return t.sliceRange(e)}}class M extends n.DateComponent{constructor(){super(...arguments),this.slicer=new P,this.tableRef=s.createRef()}render(){let{props:e,context:t}=this;return s.createElement(k,Object.assign({ref:this.tableRef},this.slicer.sliceProps(e,e.dateProfile,e.nextDayThreshold,t,e.dayTableModel),{dateProfile:e.dateProfile,cells:e.dayTableModel.cells,colGroupNode:e.colGroupNode,tableMinWidth:e.tableMinWidth,renderRowIntro:e.renderRowIntro,dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,showWeekNumbers:e.showWeekNumbers,expandRows:e.expandRows,headerAlignElRef:e.headerAlignElRef,clientWidth:e.clientWidth,clientHeight:e.clientHeight,forPrint:e.forPrint}))}}class T extends i{constructor(){super(...arguments),this.buildDayTableModel=n.memoize(H),this.headerRef=s.createRef(),this.tableRef=s.createRef()}render(){let{options:e,dateProfileGenerator:t}=this.context,{props:i}=this,r=this.buildDayTableModel(i.dateProfile,t),a=e.dayHeaders&&s.createElement(n.DayHeader,{ref:this.headerRef,dateProfile:i.dateProfile,dates:r.headerDates,datesRepDistinctDays:1===r.rowCnt}),l=t=>s.createElement(M,{ref:this.tableRef,dateProfile:i.dateProfile,dayTableModel:r,businessHours:i.businessHours,dateSelection:i.dateSelection,eventStore:i.eventStore,eventUiBases:i.eventUiBases,eventSelection:i.eventSelection,eventDrag:i.eventDrag,eventResize:i.eventResize,nextDayThreshold:e.nextDayThreshold,colGroupNode:t.tableColGroupNode,tableMinWidth:t.tableMinWidth,dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,showWeekNumbers:e.weekNumbers,expandRows:!i.isHeightAuto,headerAlignElRef:this.headerElRef,clientWidth:t.clientWidth,clientHeight:t.clientHeight,forPrint:i.forPrint});return e.dayMinWidth?this.renderHScrollLayout(a,l,r.colCnt,e.dayMinWidth):this.renderSimpleLayout(a,l)}}function H(e,t){let s=new n.DaySeriesModel(e.renderRange,t);return new n.DayTableModel(s,/year|month|week/.test(e.currentRangeUnit))}class z extends n.DateProfileGenerator{buildRenderRange(e,t,n){let s=super.buildRenderRange(e,t,n),{props:i}=this;return N({currentRange:s,snapToWeek:/^(year|month)$/.test(t),fixedWeekCount:i.fixedWeekCount,dateEnv:i.dateEnv})}}function N(e){let t,{dateEnv:s,currentRange:i}=e,{start:r,end:a}=i;if(e.snapToWeek&&(r=s.startOfWeek(r),t=s.startOfWeek(a),t.valueOf()!==a.valueOf()&&(a=n.addWeeks(t,1))),e.fixedWeekCount){let e=s.startOfWeek(s.startOfMonth(n.addDays(i.end,-1))),t=Math.ceil(n.diffWeeks(e,a));a=n.addWeeks(a,6-t)}return{start:r,end:a}}n.injectStyles(':root{--fc-daygrid-event-dot-width:8px}.fc-daygrid-day-events:after,.fc-daygrid-day-events:before,.fc-daygrid-day-frame:after,.fc-daygrid-day-frame:before,.fc-daygrid-event-harness:after,.fc-daygrid-event-harness:before{clear:both;content:"";display:table}.fc .fc-daygrid-body{position:relative;z-index:1}.fc .fc-daygrid-day.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-daygrid-day-frame{min-height:100%;position:relative}.fc .fc-daygrid-day-top{display:flex;flex-direction:row-reverse}.fc .fc-day-other .fc-daygrid-day-top{opacity:.3}.fc .fc-daygrid-day-number{padding:4px;position:relative;z-index:4}.fc .fc-daygrid-month-start{font-size:1.1em;font-weight:700}.fc .fc-daygrid-day-events{margin-top:1px}.fc .fc-daygrid-body-balanced .fc-daygrid-day-events{left:0;position:absolute;right:0}.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events{min-height:2em;position:relative}.fc .fc-daygrid-body-natural .fc-daygrid-day-events{margin-bottom:1em}.fc .fc-daygrid-event-harness{position:relative}.fc .fc-daygrid-event-harness-abs{left:0;position:absolute;right:0;top:0}.fc .fc-daygrid-bg-harness{bottom:0;position:absolute;top:0}.fc .fc-daygrid-day-bg .fc-non-business{z-index:1}.fc .fc-daygrid-day-bg .fc-bg-event{z-index:2}.fc .fc-daygrid-day-bg .fc-highlight{z-index:3}.fc .fc-daygrid-event{margin-top:1px;z-index:6}.fc .fc-daygrid-event.fc-event-mirror{z-index:7}.fc .fc-daygrid-day-bottom{font-size:.85em;margin:0 2px}.fc .fc-daygrid-day-bottom:after,.fc .fc-daygrid-day-bottom:before{clear:both;content:"";display:table}.fc .fc-daygrid-more-link{border-radius:3px;cursor:pointer;line-height:1;margin-top:1px;max-width:100%;overflow:hidden;padding:2px;position:relative;white-space:nowrap;z-index:4}.fc .fc-daygrid-more-link:hover{background-color:rgba(0,0,0,.1)}.fc .fc-daygrid-week-number{background-color:var(--fc-neutral-bg-color);color:var(--fc-neutral-text-color);min-width:1.5em;padding:2px;position:absolute;text-align:center;top:0;z-index:5}.fc .fc-more-popover .fc-popover-body{min-width:220px;padding:10px}.fc-direction-ltr .fc-daygrid-event.fc-event-start,.fc-direction-rtl .fc-daygrid-event.fc-event-end{margin-left:2px}.fc-direction-ltr .fc-daygrid-event.fc-event-end,.fc-direction-rtl .fc-daygrid-event.fc-event-start{margin-right:2px}.fc-direction-ltr .fc-daygrid-more-link{float:left}.fc-direction-ltr .fc-daygrid-week-number{border-radius:0 0 3px 0;left:0}.fc-direction-rtl .fc-daygrid-more-link{float:right}.fc-direction-rtl .fc-daygrid-week-number{border-radius:0 0 0 3px;right:0}.fc-liquid-hack .fc-daygrid-day-frame{position:static}.fc-daygrid-event{border-radius:3px;font-size:var(--fc-small-font-size);position:relative;white-space:nowrap}.fc-daygrid-block-event .fc-event-time{font-weight:700}.fc-daygrid-block-event .fc-event-time,.fc-daygrid-block-event .fc-event-title{padding:1px}.fc-daygrid-dot-event{align-items:center;display:flex;padding:2px 0}.fc-daygrid-dot-event .fc-event-title{flex-grow:1;flex-shrink:1;font-weight:700;min-width:0;overflow:hidden}.fc-daygrid-dot-event.fc-event-mirror,.fc-daygrid-dot-event:hover{background:rgba(0,0,0,.1)}.fc-daygrid-dot-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-daygrid-event-dot{border:calc(var(--fc-daygrid-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-daygrid-event-dot-width)/2);box-sizing:content-box;height:0;margin:0 4px;width:0}.fc-direction-ltr .fc-daygrid-event .fc-event-time{margin-right:3px}.fc-direction-rtl .fc-daygrid-event .fc-event-time{margin-left:3px}');var W=t.createPlugin({name:"@fullcalendar/daygrid",initialView:"dayGridMonth",views:{dayGrid:{component:T,dateProfileGeneratorClass:z},dayGridDay:{type:"dayGrid",duration:{days:1}},dayGridWeek:{type:"dayGrid",duration:{weeks:1}},dayGridMonth:{type:"dayGrid",duration:{months:1},fixedWeekCount:!0},dayGridYear:{type:"dayGrid",duration:{years:1}}}}),I={__proto__:null,DayTable:M,DayTableSlicer:P,TableDateProfileGenerator:z,buildDayTableRenderRange:N,Table:k,TableRows:C,TableView:i,buildDayTableModel:H,DayGridView:T};return t.globalPlugins.push(W),e.Internal=I,e.default=W,Object.defineProperty(e,"__esModule",{value:!0}),e}({},FullCalendar,FullCalendar.Internal,FullCalendar.Preact);