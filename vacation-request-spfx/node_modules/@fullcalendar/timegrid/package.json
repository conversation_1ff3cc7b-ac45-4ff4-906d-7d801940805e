{"name": "@fullcalendar/timegrid", "version": "6.1.18", "title": "FullCalendar Time Grid Plugin", "description": "Display events on time slots", "keywords": ["calendar", "event", "full-sized", "fullcalendar", "time", "slots"], "homepage": "https://fullcalendar.io/docs/timegrid-view", "dependencies": {"@fullcalendar/daygrid": "~6.1.18"}, "peerDependencies": {"@fullcalendar/core": "~6.1.18"}, "type": "module", "bugs": "https://fullcalendar.io/reporting-bugs", "repository": {"type": "git", "url": "https://github.com/fullcalendar/fullcalendar.git", "directory": "packages/timegrid"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://arshaw.com/"}, "copyright": "2024 <PERSON>", "types": "./index.d.ts", "main": "./index.cjs", "module": "./index.js", "unpkg": "./index.global.min.js", "jsdelivr": "./index.global.min.js", "exports": {"./package.json": "./package.json", "./index.cjs": "./index.cjs", "./index.js": "./index.js", ".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.js"}, "./internal.cjs": "./internal.cjs", "./internal.js": "./internal.js", "./internal": {"types": "./internal.d.ts", "require": "./internal.cjs", "import": "./internal.js"}}, "sideEffects": false}