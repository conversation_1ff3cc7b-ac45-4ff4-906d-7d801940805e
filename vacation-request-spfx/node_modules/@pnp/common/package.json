{"name": "@pnp/common", "version": "2.15.0", "description": "pnp - provides shared functionality across all pnp libraries", "main": "./index.js", "typings": "./index", "dependencies": {"tslib": "2.3.0"}, "author": {"name": "Microsoft and other contributors"}, "license": "MIT", "bugs": {"url": "https://github.com/pnp/pnpjs/issues"}, "homepage": "https://github.com/pnp/pnpjs", "repository": {"type": "git", "url": "git:github.com/pnp/pnpjs"}, "funding": {"type": "individual", "url": "https://github.com/sponsors/patrick-rodgers/"}, "type": "module"}