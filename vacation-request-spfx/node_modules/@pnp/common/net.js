import { __awaiter, __extends, __generator } from "tslib";
import { assign, objectDefinedNotNull } from "./util.js";
import { safeGlobal } from "./safe-global.js";
export function mergeHeaders(target, source) {
    if (objectDefinedNotNull(source)) {
        var temp = new Request("", { headers: source });
        temp.headers.forEach(function (value, name) {
            target.append(name, value);
        });
    }
}
export function mergeOptions(target, source) {
    if (objectDefinedNotNull(source)) {
        var headers = assign(target.headers || {}, source.headers);
        target = assign(target, source);
        target.headers = headers;
    }
}
/**
 * Parses out the root of the request url to use as the resource when getting the token
 *
  * @param url The url to parse
 */
export function getADALResource(url) {
    var u = new URL(url);
    return u.protocol + "//" + u.hostname;
}
/**
 * Makes requests using the global/window fetch API
 */
var FetchClient = /** @class */ (function () {
    function FetchClient() {
    }
    FetchClient.prototype.fetch = function (url, options) {
        return safeGlobal.fetch(url, options);
    };
    return FetchClient;
}());
export { FetchClient };
/**
 * Makes requests using the fetch API adding the supplied token to the Authorization header
 */
var BearerTokenFetchClient = /** @class */ (function (_super) {
    __extends(BearerTokenFetchClient, _super);
    function BearerTokenFetchClient(token) {
        var _this = _super.call(this) || this;
        _this.token = token;
        return _this;
    }
    BearerTokenFetchClient.prototype.fetch = function (url, options) {
        if (options === void 0) { options = {}; }
        var headers = new Headers();
        mergeHeaders(headers, options.headers);
        headers.set("Authorization", "Bearer " + this.token);
        options.headers = headers;
        return _super.prototype.fetch.call(this, url, options);
    };
    return BearerTokenFetchClient;
}(FetchClient));
export { BearerTokenFetchClient };
var LambdaFetchClient = /** @class */ (function (_super) {
    __extends(LambdaFetchClient, _super);
    function LambdaFetchClient(tokenFactory) {
        var _this = _super.call(this, null) || this;
        _this.tokenFactory = tokenFactory;
        return _this;
    }
    /**
     * Executes a fetch request using the supplied url and options
     *
     * @param url Absolute url of the request
     * @param options Any options
     */
    LambdaFetchClient.prototype.fetch = function (url, options) {
        return __awaiter(this, void 0, void 0, function () {
            var _a;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _a = this;
                        return [4 /*yield*/, this.tokenFactory({ url: url, options: options })];
                    case 1:
                        _a.token = _b.sent();
                        return [2 /*return*/, _super.prototype.fetch.call(this, url, options)];
                }
            });
        });
    };
    return LambdaFetchClient;
}(BearerTokenFetchClient));
export { LambdaFetchClient };
/**
 * Client wrapping the aadTokenProvider available from SPFx >= 1.6
 */
var SPFxAdalClient = /** @class */ (function (_super) {
    __extends(SPFxAdalClient, _super);
    /**
     *
     * @param context provide the appropriate SPFx Context object
     */
    function SPFxAdalClient(context) {
        var _this = _super.call(this, function (params) { return __awaiter(_this, void 0, void 0, function () {
            var provider;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, context.aadTokenProviderFactory.getTokenProvider()];
                    case 1:
                        provider = _a.sent();
                        return [2 /*return*/, provider.getToken(getADALResource(params.url))];
                }
            });
        }); }) || this;
        _this.context = context;
        return _this;
    }
    /**
     * Gets an AAD token for the provided resource using the SPFx AADTokenProvider
     *
     * @param resource Resource for which a token is to be requested (ex: https://graph.microsoft.com)
     */
    SPFxAdalClient.prototype.getToken = function (resource) {
        return __awaiter(this, void 0, void 0, function () {
            var provider;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.context.aadTokenProviderFactory.getTokenProvider()];
                    case 1:
                        provider = _a.sent();
                        return [2 /*return*/, provider.getToken(resource)];
                }
            });
        });
    };
    return SPFxAdalClient;
}(LambdaFetchClient));
export { SPFxAdalClient };
//# sourceMappingURL=net.js.map