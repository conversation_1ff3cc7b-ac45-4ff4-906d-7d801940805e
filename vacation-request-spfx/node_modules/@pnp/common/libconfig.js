import { __read, __values } from "tslib";
import { mergeMaps, objectToMap } from "./collections.js";
export function setup(config, runtime) {
    if (runtime === void 0) { runtime = DefaultRuntime; }
    runtime.assign(config);
}
// lable mapping for known config values
var s = [
    "defaultCachingStore",
    "defaultCachingTimeoutSeconds",
    "globalCacheDisable",
    "enableCacheExpiration",
    "cacheExpirationIntervalMilliseconds",
    "spfxContext",
    "ie11",
];
var runtimeCreateHooks = [];
export function onRuntimeCreate(hook) {
    if (runtimeCreateHooks.indexOf(hook) < 0) {
        // apply hook logic to default runtime
        hook(DefaultRuntime);
        runtimeCreateHooks.push(hook);
    }
}
var Runtime = /** @class */ (function () {
    function Runtime(_v) {
        var _this = this;
        if (_v === void 0) { _v = new Map(); }
        this._v = _v;
        var defaulter = function (key, def) {
            if (!_this._v.has(key)) {
                _this._v.set(key, def);
            }
        };
        // setup defaults
        defaulter(s[0], "session");
        defaulter(s[1], 60);
        defaulter(s[2], false);
        defaulter(s[3], false);
        defaulter(s[4], 750);
        defaulter(s[5], null);
        defaulter(s[6], false);
        runtimeCreateHooks.forEach(function (hook) { return hook(_this); });
    }
    /**
     *
     * @param config The set of properties to add to this runtime instance
     */
    Runtime.prototype.assign = function (config) {
        this._v = mergeMaps(this._v, objectToMap(config));
    };
    /**
     * Gets a runtime value using T to define the available keys, and R to define the type returned by that key
     *
     * @param key
     */
    Runtime.prototype.get = function (key) {
        return this._v.get(key);
    };
    /**
     * Exports the internal Map representing this runtime
     */
    Runtime.prototype.export = function () {
        var e_1, _a;
        var expt = new Map();
        try {
            for (var _b = __values(this._v), _c = _b.next(); !_c.done; _c = _b.next()) {
                var _d = __read(_c.value, 2), key = _d[0], value = _d[1];
                if (key !== "__isDefault__") {
                    expt.set(key, value);
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        return expt;
    };
    return Runtime;
}());
export { Runtime };
// default runtime used globally
var _runtime = new Runtime(new Map([["__isDefault__", true]]));
export var DefaultRuntime = _runtime;
//# sourceMappingURL=libconfig.js.map