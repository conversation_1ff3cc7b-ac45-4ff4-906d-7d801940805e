{"version": 3, "file": "storage.js", "sourceRoot": "", "sources": ["../../../../packages/common/storage.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,EAAE,oBAAoB,EAAE,MAAM,WAAW,CAAC;AAC/E,OAAO,EAAyB,cAAc,EAAE,MAAM,gBAAgB,CAAC;AAEvE;;;GAGG;AACH;IAOI;;;;OAIG;IACH,iCAAoB,KAAc,EAAS,qBAA0B;QAA1B,sCAAA,EAAA,yBAAyB,CAAC;QAAjD,UAAK,GAAL,KAAK,CAAS;QAAS,0BAAqB,GAArB,qBAAqB,CAAK;QAEjE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAE3B,mDAAmD;QACnD,iEAAiE;QACjE,IAAI,cAAc,CAAC,GAAG,CAAiC,uBAAuB,CAAC,EAAE;YAC7E,IAAI,CAAC,sBAAsB,EAAE,CAAC;SACjC;IACL,CAAC;IAEa,4BAAI,GAAlB,UAAmB,KAAc;QAC7B,OAAO,IAAI,uBAAuB,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACrG,CAAC;IAED;;;;OAIG;IACI,qCAAG,GAAV,UAAc,GAAW;QAErB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,IAAI,CAAC;SACf;QAED,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAElC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC;SACf;QAED,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAElC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,IAAI,IAAI,EAAE,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,OAAO,IAAI,CAAC;SAEf;aAAM;YAEH,OAAO,WAAW,CAAC,KAAU,CAAC;SACjC;IACL,CAAC;IAED;;;;;;OAMG;IACI,qCAAG,GAAV,UAAW,GAAW,EAAE,CAAM,EAAE,MAAa;QACzC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;SAC9D;IACL,CAAC;IAED;;;;OAIG;IACI,wCAAM,GAAb,UAAc,GAAW;QACrB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SAC9B;IACL,CAAC;IAED;;;;;;OAMG;IACU,0CAAQ,GAArB,UAAyB,GAAW,EAAE,MAAwB,EAAE,MAAa;;;;;;wBAEzE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;4BACf,sBAAO,MAAM,EAAE,EAAC;yBACnB;wBAEG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAI,GAAG,CAAC,CAAC;6BAErB,CAAA,CAAC,KAAK,IAAI,CAAA,EAAV,wBAAU;wBACN,qBAAM,MAAM,EAAE,EAAA;;wBAAlB,CAAC,GAAG,SAAc,CAAC;wBACnB,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;;4BAG7B,sBAAO,CAAC,EAAC;;;;KACZ;IAED;;OAEG;IACU,+CAAa,GAA1B;;;;;;wBAEI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;4BACf,sBAAO;yBACV;wBAEQ,CAAC,GAAG,CAAC;;;6BAAE,CAAA,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA;wBAC3B,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;6BAC1B,CAAA,GAAG,KAAK,IAAI,CAAA,EAAZ,wBAAY;6BAER,wBAAwB,CAAC,IAAI,CAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAA9D,wBAA8D;wBAC9D,oEAAoE;wBACpE,qBAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAA;;wBADnB,oEAAoE;wBACpE,SAAmB,CAAC;;;wBANO,CAAC,EAAE,CAAA;;;;;;KAU7C;IAED;;OAEG;IACK,sCAAI,GAAZ;QACI,IAAM,GAAG,GAAG,GAAG,CAAC;QAChB,IAAI;YACA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC3B,OAAO,IAAI,CAAC;SACf;QAAC,OAAO,CAAC,EAAE;YACR,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACK,mDAAiB,GAAzB,UAA0B,CAAM,EAAE,MAAa;QAC3C,IAAI,MAAM,KAAK,SAAS,EAAE;YAEtB,kEAAkE;YAClE,IAAI,cAAc,GAAG,cAAc,CAAC,GAAG,CAAgC,8BAA8B,CAAC,CAAC;YACvG,IAAI,IAAI,CAAC,qBAAqB,GAAG,CAAC,EAAE;gBAChC,cAAc,GAAG,IAAI,CAAC,qBAAqB,GAAG,EAAE,CAAC;aACpD;YACD,MAAM,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;SAC1D;QAED,OAAO,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,wDAAsB,GAA9B;QAAA,iBAWC;QATG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC;YAEtB,6BAA6B;YAC7B,UAAU,CAAC,cAAc,CAAC,KAAI,EAAE,KAAI,CAAC,sBAAsB,CAAC,EAAE,cAAc,CAAC,GAAG,CAAgC,qCAAqC,CAAC,CAAC,CAAC;QAC5J,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IACL,8BAAC;AAAD,CAAC,AA1KD,IA0KC;;AAiDD;;GAEG;AACH;IAEI,uBAAoB,MAA+B;QAA/B,uBAAA,EAAA,aAAa,GAAG,EAAe;QAA/B,WAAM,GAAN,MAAM,CAAyB;IAAI,CAAC;IAKxD,sBAAW,iCAAM;aAAjB;YACI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QAC5B,CAAC;;;OAAA;IAEM,6BAAK,GAAZ;QACI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAEM,+BAAO,GAAd,UAAe,GAAW;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAEM,2BAAG,GAAV,UAAW,KAAa;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAEM,kCAAU,GAAjB,UAAkB,GAAW;QACzB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAEM,+BAAO,GAAd,UAAe,GAAW,EAAE,IAAY;QACpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC;IACL,oBAAC;AAAD,CAAC,AA9BD,IA8BC;AAED;;GAEG;AACH;IAEI;;;;OAIG;IACH,0BAAoB,MAAqC,EAAU,QAAuC;QAAtF,uBAAA,EAAA,aAAqC;QAAU,yBAAA,EAAA,eAAuC;QAAtF,WAAM,GAAN,MAAM,CAA+B;QAAU,aAAQ,GAAR,QAAQ,CAA+B;IAAI,CAAC;IAK/G,sBAAW,mCAAK;QAHhB;;WAEG;aACH;YAEI,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;gBACtB,IAAI,CAAC,MAAM,GAAG,IAAI,uBAAuB,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;aACzH;YAED,OAAO,IAAI,CAAC,MAAM,CAAC;QACvB,CAAC;;;OAAA;IAKD,sBAAW,qCAAO;QAHlB;;WAEG;aACH;YAEI,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;gBACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,uBAAuB,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;aAC/H;YAED,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB,CAAC;;;OAAA;IACL,uBAAC;AAAD,CAAC,AAhCD,IAgCC"}