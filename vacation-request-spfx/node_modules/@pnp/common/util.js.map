{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../packages/common/util.ts"], "names": [], "mappings": "AAEA;;;;;;;GAOG;AACH,wDAAwD;AACxD,MAAM,UAAU,cAAc,CAAC,OAAY,EAAE,MAAgB;IAAE,gBAAgB;SAAhB,UAAgB,EAAhB,qBAAgB,EAAhB,IAAgB;QAAhB,+BAAgB;;IAC3E,OAAO;QACH,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAClC,CAAC,CAAC;AACN,CAAC;AAID;;;;;;;;GAQG;AACH,MAAM,UAAU,OAAO,CAAC,IAAU,EAAE,QAAyB,EAAE,KAAa;IACxE,IAAI,GAAG,GAAqB,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,6BAA6B;IACpF,QAAQ,QAAQ,CAAC,WAAW,EAAE,EAAE;QAC5B,KAAK,MAAM;YAAE,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QAC/D,KAAK,SAAS;YAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QAChE,KAAK,OAAO;YAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QAC1D,KAAK,MAAM;YAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QAC3D,KAAK,KAAK;YAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QACtD,KAAK,MAAM;YAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,GAAG,OAAO,CAAC,CAAC;YAAC,MAAM;QACjE,KAAK,QAAQ;YAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QACjE,KAAK,QAAQ;YAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;YAAC,MAAM;QAChE;YAAS,GAAG,GAAG,SAAS,CAAC;YAAC,MAAM;KACnC;IACD,OAAO,GAAG,CAAC;AACf,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO;IAAC,eAAkB;SAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;QAAlB,0BAAkB;;IAEtC,OAAO,KAAK;SACP,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAA1B,CAA0B,CAAC;SAC1C,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAlD,CAAkD,CAAC;SAC/D,IAAI,CAAC,GAAG,CAAC;SACT,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC7B,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,eAAe,CAAC,KAAa;IACzC,IAAM,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QAC5B,IAAI,CAAC,CAAC,CAAC,GAAG,gEAAgE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;KACrH;IACD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,CAAC;AAED;;;;GAIG;AACH,+BAA+B;AAC/B,MAAM,UAAU,OAAO;IACnB,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACnB,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC;QACtE,IAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QACvB,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC1D,CAAC,CAAC,CAAC;AACP,CAAC;AACD,8BAA8B;AAE9B;;;;GAIG;AACH,MAAM,UAAU,MAAM,CAAC,CAAM;IACzB,OAAO,OAAO,CAAC,KAAK,UAAU,CAAC;AACnC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,oBAAoB,CAAC,GAAQ;IACzC,OAAO,OAAO,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,IAAI,CAAC;AACtD,CAAC;AAED;;EAEE;AACF,MAAM,UAAU,OAAO,CAAC,KAAU;IAE9B,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC;AAC3H,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,MAAM,CAAmE,MAAS,EAAE,MAAS,EAAE,WAAmB,EAC9H,MAAkD;IADyD,4BAAA,EAAA,mBAAmB;IAC9H,uBAAA,EAAA,uBAA8C,OAAA,IAAI,EAAJ,CAAI;IAElD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE;QAC/B,OAAc,MAAM,CAAC;KACxB;IAED,6DAA6D;IAC7D,IAAM,KAAK,GAAmC,WAAW,CAAC,CAAC,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAT,CAAS,CAAC,CAAC,CAAC,cAAM,OAAA,IAAI,EAAJ,CAAI,CAAC;IAE7F,2BAA2B;IAC3B,IAAM,CAAC,GAAG,UAAC,CAAS,IAAK,OAAA,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAA7B,CAA6B,CAAC;IAEvD,OAAO,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC;SACpC,MAAM,CAAC,CAAC,CAAC;SACT,MAAM,CAAC,UAAC,CAAM,EAAE,CAAS;QACtB,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QACjB,OAAO,CAAC,CAAC;IACb,CAAC,EAAE,MAAM,CAAC,CAAC;AACnB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,aAAa,CAAC,GAAW;IACrC,OAAO,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,mBAAmB,CAAC,CAAS;IACzC,OAAO,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACzD,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,IAAY;IAErC,IAAI,mBAAmB,CAAC,IAAI,CAAC,EAAE;QAC3B,OAAO,IAAI,CAAC;KACf;IAED,IAAM,OAAO,GAAG,iEAAiE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAE7F,OAAO,OAAO,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AAChD,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,GAAG,CAAC,CAAM;IACtB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7B,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,GAAG,CAAC,CAAM,EAAE,CAAS;IACjC,OAAO,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,CAAC;AAED;;;;GAIG;AACH,+BAA+B;AAC/B,MAAM,UAAU,WAAW,CAAC,CAAS;IACjC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QAChB,OAAO,IAAI,CAAC;KACf;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,IAAM,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QAClC,IAAI,IAAI,CAAC,CAAC,CAAC,2BAA2B;KACzC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AACD,8BAA8B"}