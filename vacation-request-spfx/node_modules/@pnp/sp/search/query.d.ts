import { _SPInstance, SPInit } from "../spqueryable.js";
import { ISearchQuery, ISearchResponse, ISearchResult, ISearchBuilder, SearchQueryInit } from "./types.js";
/**
 * Creates a new instance of the SearchQueryBuilder
 *
 * @param queryText Initial query text
 * @param _query Any initial query configuration
 */
export declare function SearchQueryBuilder(queryText?: string, _query?: {}): ISearchBuilder;
/**
 * Describes the search API
 *
 */
export declare class _Search extends _SPInstance {
    /**
     * @returns Promise
     */
    run(queryInit: SearchQueryInit): Promise<SearchResults>;
    /**
     * Fix array property
     *
     * @param prop property to fix for container struct
     */
    private fixArrProp;
    /**
     * Translates one of the query initializers into a SearchQuery instance
     *
     * @param query
     */
    private parseQuery;
}
export interface ISearch extends Pick<_Search, "run" | "using"> {
    (init: SearchQueryInit): Promise<SearchResults>;
}
export declare const Search: (base: SPInit, path?: string) => ISearch;
export declare class SearchResults {
    private _search;
    private _query;
    private _raw;
    private _primary;
    constructor(rawResponse: any, _search: _Search, _query: ISearchQuery, _raw?: ISearchResponse, _primary?: ISearchResult[]);
    get ElapsedTime(): number;
    get RowCount(): number;
    get TotalRows(): number;
    get TotalRowsIncludingDuplicates(): number;
    get RawSearchResults(): ISearchResponse;
    get PrimarySearchResults(): ISearchResult[];
    /**
     * Gets a page of results
     *
     * @param pageNumber Index of the page to return. Used to determine StartRow
     * @param pageSize Optional, items per page (default = 10)
     */
    getPage(pageNumber: number, pageSize?: number): Promise<SearchResults>;
    /**
     * Formats a search results array
     *
     * @param rawResults The array to process
     */
    protected formatSearchResults(rawResults: any): ISearchResult[];
}
//# sourceMappingURL=query.d.ts.map