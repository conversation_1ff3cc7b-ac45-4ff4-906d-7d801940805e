import { _SPCollection, _SPInstance } from "../spqueryable.js";
export declare class _Comments extends _SPCollection<ICommentInfo[]> {
    /**
     * Adds a new comment to this collection
     *
     * @param info Comment information to add
     */
    add(info: string | ICommentInfo): Promise<IComment & ICommentInfo>;
    /**
     * Gets a comment by id
     *
     * @param id Id of the comment to load
     */
    getById(id: string | number): IComment;
    /**
     * Deletes all the comments in this collection
     */
    clear(): Promise<boolean>;
}
export interface IComments extends _Comments {
}
export declare const Comments: import("../spqueryable.js").ISPInvokableFactory<IComments>;
export declare class _Comment extends _SPInstance<ICommentInfo> {
    /**
     * A comment's replies
     */
    get replies(): IReplies;
    /**
     * Likes the comment as the current user
     */
    like(): Promise<void>;
    /**
     * Unlikes the comment as the current user
     */
    unlike(): Promise<void>;
    /**
     * Deletes this comment
     */
    delete(): Promise<void>;
}
export interface IComment extends _Comment {
}
export declare const Comment: import("../spqueryable.js").ISPInvokableFactory<IComment>;
export declare class _Replies extends _SPCollection<ICommentInfo[]> {
    /**
     * Adds a new reply to this collection
     *
     * @param info Comment information to add
     */
    add(info: string | Partial<ICommentInfo>): Promise<IComment & ICommentInfo>;
}
export interface IReplies extends _Replies {
}
export declare const Replies: import("../spqueryable.js").ISPInvokableFactory<IReplies>;
/**
 * Defines the information for a comment author
 */
export interface ICommentAuthorData {
    email: string;
    id: number;
    isActive: boolean;
    isExternal: boolean;
    jobTitle: string | null;
    loginName: string;
    name: string;
    principalType: number;
    userId: any | null;
}
/**
 * Defines the information for a comment
 */
export interface ICommentInfo {
    author: ICommentAuthorData;
    createdDate: string;
    id: string;
    isLikedByUser: boolean;
    isReply: boolean;
    itemId: number;
    likeCount: number;
    listId: string;
    mentions: [
        {
            loginName: string;
            email: string;
            name: string;
        }
    ] | null;
    parentId: string;
    replyCount: number;
    text: string;
}
export interface ILikeData {
    name: string;
    loginName: string;
    id: number;
    email: string;
    creationDate: string;
}
export interface ILikedByInformation {
    likedBy: {
        creationDate: string;
        email: string;
        id: number;
        loginName: string;
        name: string;
    }[];
    isLikedByUser: boolean;
    likeCount: number;
}
export type RatingValues = 1 | 2 | 3 | 4 | 5;
//# sourceMappingURL=types.d.ts.map