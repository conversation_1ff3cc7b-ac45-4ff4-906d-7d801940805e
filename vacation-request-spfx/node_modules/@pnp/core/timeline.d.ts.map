{"version": 3, "file": "timeline.d.ts", "sourceRoot": "", "sources": ["../../../packages/core/timeline.ts"], "names": [], "mappings": "AAGA;;GAEG;AACH,MAAM,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;AAE3E;;GAEG;AACH,MAAM,MAAM,oBAAoB,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AAEvF;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC;AAE5F;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG,cAAc,GAAG,gBAAgB,CAAC;AAE9D;;GAEG;AACH,MAAM,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC;AAE/G;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG,MAAM,CAAC,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC;AAEjE;;GAEG;AACH,KAAK,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,CAAC,SAAS,OAAO,GAAG,CAAC,IACtD;KAAG,IAAI,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG;QAC1B,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvD,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5C,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9D,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC9D,KAAK,IAAI,OAAO,CAAC;KACpB;CACA,CAAC;AAEN;;GAEG;AACH,KAAK,cAAc,CAAC,CAAC,SAAS,OAAO,IACjC;KAAG,IAAI,IAAI,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;CAAE,CAAC;AAExI;;GAEG;AACH,KAAK,sBAAsB,CAAC,CAAC,SAAS,OAAO,IAAI;IAC7C,IAAI,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAC3E,OAAO,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAC9E,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;IAC1G,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,MAAM,GAAG,KAAK,KAAK,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC;CACpG,CAAC;AAEF;;GAEG;AACH,KAAK,WAAW,CAAC,CAAC,SAAS,OAAO,IAAI,YAAY,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAEnG;;GAEG;AACH,KAAK,aAAa,CAAC,CAAC,SAAS,OAAO,IAAI,cAAc,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;AAEtG;;GAEG;AACH,MAAM,MAAM,YAAY,CAAC,CAAC,SAAS,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;AAqC5E;;;GAGG;AACH,eAAO,MAAM,SAAS,6CAA4C,CAAC;AAEnE;;;;;GAKG;AACH,eAAO,MAAM,IAAI,6CAAuC,CAAC;AAQzD;;;GAGG;AACH,8BAAsB,QAAQ,CAAC,CAAC,SAAS,OAAO;IAYhC,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;IAAE,SAAS,CAAC,SAAS,EAAE,kBAAkB;IAVlF,OAAO,CAAC,QAAQ,CAA6B;IAC7C,OAAO,CAAC,UAAU,CAA6B;IAC/C,SAAS,CAAC,oBAAoB,EAAE,OAAO,CAAC;IAExC;;;;;OAKG;gBAC4B,OAAO,EAAE,CAAC,EAAY,SAAS,GAAE,kBAAuB;IAIvF;;;;;OAKG;IACI,KAAK,CAAC,GAAG,SAAS,EAAE,YAAY,EAAE,GAAG,IAAI;IAShD;;OAEG;IACH,IAAW,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,CA2C9B;IAED;;;;;OAKG;IACI,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,SAAI,GAAG,IAAI;IAI5C;;;;OAIG;IACH,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI;IAM9B;;OAEG;IACH,SAAS,KAAK,IAAI,IAAI,aAAa,CAAC,CAAC,CAAC,CAiDrC;IAED;;;;;;;OAOG;IACH,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IA6BzC;;;;OAIG;IACH,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;IAEpD;;;;OAIG;IACH,SAAS,CAAC,sBAAsB;CAMnC;AAyCD,wBAAgB,uBAAuB,CAAC,MAAM,EAAE,kBAAkB,GAAG,kBAAkB,CAQtF"}