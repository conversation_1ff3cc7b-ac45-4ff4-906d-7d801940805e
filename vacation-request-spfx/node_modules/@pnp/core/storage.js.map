{"version": 3, "file": "storage.js", "sourceRoot": "", "sources": ["../../../packages/core/storage.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,oBAAoB,EAAE,MAAM,WAAW,CAAC;AAE/D,IAAI,WAAsC,CAAC;AAE3C,SAAS,cAAc;IACnB,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;QACpC,WAAW,GAAG,IAAI,aAAa,EAAE,CAAC;KACrC;IACD,OAAO,WAAW,CAAC;AACvB,CAAC;AAED;;;GAGG;AACH,MAAM,OAAO,uBAAuB;IAOhC;;;;OAIG;IACH,YAAoB,KAAc;QAAd,UAAK,GAAL,KAAK,CAAS;QAE9B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;IAC/B,CAAC;IAED;;;;OAIG;IACI,GAAG,CAAI,GAAW;QAErB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,IAAI,CAAC;SACf;QAED,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAElC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE;YAC1B,OAAO,IAAI,CAAC;SACf;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAElC,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,IAAI,IAAI,EAAE,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACjB,OAAO,IAAI,CAAC;SAEf;aAAM;YAEH,OAAO,WAAW,CAAC,KAAU,CAAC;SACjC;IACL,CAAC;IAED;;;;;;OAMG;IACI,GAAG,CAAC,GAAW,EAAE,CAAM,EAAE,MAAa;QACzC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;SAC9D;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,GAAW;QACrB,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SAC9B;IACL,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,QAAQ,CAAI,GAAW,EAAE,MAAwB,EAAE,MAAa;QAEzE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO,MAAM,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAI,GAAG,CAAC,CAAC;QAEzB,IAAI,CAAC,KAAK,IAAI,EAAE;YACZ,CAAC,GAAG,MAAM,MAAM,EAAE,CAAC;YACnB,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;SAC5B;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa;QAEtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC9B,IAAI,GAAG,KAAK,IAAI,EAAE;gBACd,8CAA8C;gBAC9C,IAAI,wBAAwB,CAAC,IAAI,CAAS,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;oBAChE,oEAAoE;oBACpE,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;iBACvB;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACK,IAAI;QACR,MAAM,GAAG,GAAG,GAAG,CAAC;QAChB,IAAI;YACA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC3B,OAAO,IAAI,CAAC;SACf;QAAC,OAAO,CAAC,EAAE;YACR,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,CAAM,EAAE,MAAa;QAC3C,IAAI,MAAM,KAAK,SAAS,EAAE;YAEtB,MAAM,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;SAC7C;QAED,OAAO,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;IACzD,CAAC;CACJ;AAiDD;;GAEG;AACH,MAAM,aAAa;IAEf,YAAoB,SAAS,IAAI,GAAG,EAAe;QAA/B,WAAM,GAAN,MAAM,CAAyB;IAAI,CAAC;IAKxD,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IAC5B,CAAC;IAEM,KAAK;QACR,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAEM,OAAO,CAAC,GAAW;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAEM,GAAG,CAAC,KAAa;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC;IAEM,UAAU,CAAC,GAAW;QACzB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IAEM,OAAO,CAAC,GAAW,EAAE,IAAY;QACpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAC/B,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,gBAAgB;IAEzB;;;;OAIG;IACH,YAAoB,SAAiC,IAAI,EAAU,WAAmC,IAAI;QAAtF,WAAM,GAAN,MAAM,CAA+B;QAAU,aAAQ,GAAR,QAAQ,CAA+B;IAAI,CAAC;IAE/G;;OAEG;IACH,IAAW,KAAK;QAEZ,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;YACtB,IAAI,CAAC,MAAM,GAAG,IAAI,uBAAuB,CAAC,OAAO,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;SACpH;QAED,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,IAAW,OAAO;QAEd,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,uBAAuB,CAAC,OAAO,cAAc,KAAK,WAAW,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;SAC1H;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;CACJ"}