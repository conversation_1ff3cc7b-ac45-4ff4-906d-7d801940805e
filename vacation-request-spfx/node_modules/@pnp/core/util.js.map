{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../packages/core/util.ts"], "names": [], "mappings": "AAEA;;;;;;;;GAQG;AACH,MAAM,UAAU,OAAO,CAAC,IAAU,EAAE,QAAyB,EAAE,KAAa;IACxE,IAAI,GAAG,GAAqB,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,6BAA6B;IACpF,QAAQ,QAAQ,CAAC,WAAW,EAAE,EAAE;QAC5B,KAAK,MAAM;YAAE,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QAC/D,KAAK,SAAS;YAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QAChE,KAAK,OAAO;YAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QAC1D,KAAK,MAAM;YAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QAC3D,KAAK,KAAK;YAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QACtD,KAAK,MAAM;YAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,GAAG,OAAO,CAAC,CAAC;YAAC,MAAM;QACjE,KAAK,QAAQ;YAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC;YAAC,MAAM;QACjE,KAAK,QAAQ;YAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC;YAAC,MAAM;QAChE;YAAS,GAAG,GAAG,SAAS,CAAC;YAAC,MAAM;KACnC;IACD,OAAO,GAAG,CAAC;AACf,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAAC,GAAG,KAAoC;IAE3D,OAAO,KAAK;SACP,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;SAC1C,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;SAC/D,IAAI,CAAC,GAAG,CAAC;SACT,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC7B,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,eAAe,CAAC,KAAa;IACzC,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;IAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QAC5B,IAAI,CAAC,CAAC,CAAC,GAAG,gEAAgE,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;KACrH;IACD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzB,CAAC;AAED;;;;GAIG;AACH,+BAA+B;AAC/B,MAAM,UAAU,OAAO;IACnB,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACnB,OAAO,sCAAsC,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC;QACtE,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QACvB,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC1D,CAAC,CAAC,CAAC;AACP,CAAC;AACD,8BAA8B;AAE9B;;;;GAIG;AACH,wDAAwD;AACxD,MAAM,UAAU,MAAM,CAAC,CAAM;IACzB,OAAO,OAAO,CAAC,KAAK,UAAU,CAAC;AACnC,CAAC;AAED;;EAEE;AACF,MAAM,UAAU,OAAO,CAAC,KAAU;IAC9B,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,aAAa,CAAC,GAAW;IACrC,OAAO,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,mBAAmB,CAAC,CAA4B;IAC5D,OAAO,OAAO,CAAC,KAAK,WAAW,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAClE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,oBAAoB,CAAI,GAAyB;IAC7D,OAAO,OAAO,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,IAAI,CAAC;AACtD,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,GAAG,CAAC,CAAM;IACtB,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7B,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,GAAG,CAAmB,CAAM,EAAE,CAAI;IAC9C,OAAO,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC5C,CAAC;AAED;;EAEE;AACF,MAAM,UAAU,WAAW,CAAC,GAAW;IACnC,MAAM,WAAW,GAAG,wBAAwB,CAAC;IAC7C,IAAI;QACA,wCAAwC;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAC;YACxB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;SACvB;QACD,OAAO,IAAI,CAAC;KACf;IAAC,OAAO,GAAG,EAAE;QACV,oEAAoE;QACpE,OAAO,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;KACnD;AACL,CAAC;AAED;;;;GAIG;AACH,+BAA+B;AAC/B,MAAM,UAAU,WAAW,CAAC,CAAS;IACjC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QAChB,OAAO,IAAI,CAAC;KACf;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,MAAM,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;QAClC,IAAI,IAAI,CAAC,CAAC,CAAC,2BAA2B;KACzC;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AACD,8BAA8B;AAE9B;;;;GAIG;AACH,MAAM,UAAU,KAAK,CAAC,EAAU;IAE5B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAmB,EAAE,EAAE;QACvC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC;AACP,CAAC"}