{"name": "@pnp/core", "version": "4.14.0", "description": "pnp - provides shared functionality across all pnp libraries", "main": "./index.js", "typings": "./index", "dependencies": {"tslib": "2.7.0"}, "type": "module", "engines": {"node": ">=18.12.0"}, "author": {"name": "Microsoft and other contributors"}, "license": "MIT", "bugs": {"url": "https://github.com/pnp/pnpjs/issues"}, "homepage": "https://github.com/pnp/pnpjs", "repository": {"type": "git", "url": "git:github.com/pnp/pnpjs"}, "maintainers": [{"name": "patrick-rodgers", "email": "<EMAIL>"}, {"name": "juli<PERSON><PERSON>er", "email": "<EMAIL>", "url": "https://julieturner.net"}, {"name": "bcameron1231", "email": "<EMAIL>", "url": "https://beaucameron.net"}], "funding": {"type": "individual", "url": "https://github.com/sponsors/patrick-rodgers/"}}