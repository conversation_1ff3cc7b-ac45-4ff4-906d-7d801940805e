![SharePoint Patterns and Practices](https://devofficecdn.azureedge.net/media/Default/PnP/sppnp.png)

The SharePoint Patterns and Practices client side libraries were created to help enable developers to do their best work, without worrying about the exact
REST api details. Built with feedback from the community they represent a way to simplify your day-to-day dev cycle while relying on tested and proven
patterns.

Please use [http://aka.ms/community/home](http://aka.ms/community/home) for the latest updates around the whole *Microsoft 365 and Power Platform Community(PnP)* initiative.
## Source & Docs

This code is managed within the [main pnp repo](https://github.com/pnp/pnpjs), please report issues and submit pull requests there.

Please see the public site for [package documentation](https://pnp.github.io/pnpjs/).

### Code of Conduct

This project has adopted the [Microsoft Open Source Code of Conduct](https://opensource.microsoft.com/codeofconduct/). For more information see the [Code of Conduct FAQ](https://opensource.microsoft.com/codeofconduct/faq/) or contact [<EMAIL>](mailto:<EMAIL>) with any additional questions or comments.

### "Sharing is Caring"

### Disclaimer
**THIS CODE IS PROVIDED *AS IS* WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING ANY IMPLIED WARRANTIES OF FITNESS FOR A PARTICULAR PURPOSE, MERCHANTABILITY, OR NON-INFRINGEMENT.**