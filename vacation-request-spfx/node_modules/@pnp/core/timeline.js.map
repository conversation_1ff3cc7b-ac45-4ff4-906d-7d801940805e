{"version": 3, "file": "timeline.js", "sourceRoot": "", "sources": ["../../../packages/core/timeline.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACpD,OAAO,EAAE,oBAAoB,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AA4ElE;;GAEG;AACH,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;AAYnD;;;;;GAKG;AACH,sCAAsC;AACtC,MAAM,MAAM,GAAG,CAAC,IAA4B,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAElG;;;;GAIG;AACH,MAAM,OAAO,GAAG,CAAC,IAA4B,EAAE,EAAE,CAAC,CAAC,CAA0B,QAAW,EAAK,EAAE;IAC3F,sCAAsC;IACtC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;IAChD,OAAO,QAAQ,CAAC;AACpB,CAAC,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,OAAO,0CAAkC,CAAC;AAEnE;;;;;GAKG;AACH,MAAM,CAAC,MAAM,IAAI,GAAG,OAAO,qCAA6B,CAAC;AAQzD;;;GAGG;AACH,MAAM,OAAgB,QAAQ;IAM1B;;;;;OAKG;IACH,YAA+B,OAAU,EAAY,YAAgC,EAAE;QAAxD,YAAO,GAAP,OAAO,CAAG;QAAY,cAAS,GAAT,SAAS,CAAyB;QAV/E,aAAQ,GAAwB,IAAI,CAAC;QACrC,eAAU,GAAwB,IAAI,CAAC;QAU3C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACrC,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,GAAG,SAAyB;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SACtB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QAET,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YAExB,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE;gBAC5B,GAAG,EAAE,CAAC,MAAW,EAAE,CAAS,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAsB,EAAE,EAAE;oBAEtE,MAAM,CAAC,sBAAsB,EAAE,CAAC;oBAChC,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,OAAO,kCAA0B,CAAC;oBACnE,OAAO,MAAM,CAAC;gBAElB,CAAC,EAAE;oBACC,OAAO,EAAE,GAAoB,EAAE;wBAC3B,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACzF,CAAC;oBACD,OAAO,EAAE,CAAC,OAAsB,EAAE,EAAE;wBAEhC,MAAM,CAAC,sBAAsB,EAAE,CAAC;wBAChC,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,OAAO,sCAA8B,CAAC;wBACvE,OAAO,MAAM,CAAC;oBAClB,CAAC;oBACD,OAAO,EAAE,CAAC,OAAsB,EAAE,EAAE;wBAEhC,MAAM,CAAC,sBAAsB,EAAE,CAAC;wBAChC,WAAW,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,OAAO,sCAA8B,CAAC;wBACvE,OAAO,MAAM,CAAC;oBAClB,CAAC;oBACD,KAAK,EAAE,GAAY,EAAE;wBAEjB,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE;4BAClC,MAAM,CAAC,sBAAsB,EAAE,CAAC;4BAChC,gDAAgD;4BAC3B,MAAM,CAAC,SAAU,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;4BACrD,OAAO,IAAI,CAAC;yBACf;wBAED,OAAO,KAAK,CAAC;oBACjB,CAAC;iBACJ,CAAC;aACL,CAAC,CAAC;SACN;QAED,OAAY,IAAI,CAAC,QAAQ,CAAC;IAC9B,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,OAAe,EAAE,KAAK,GAAG,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACO,KAAK,CAAC,CAAO;QACnB,IAAI,oBAAoB,CAAC,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACtB;IACL,CAAC;IAED;;OAEG;IACH,IAAc,IAAI;QAEd,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;YAE1B,IAAI,CAAC,UAAU,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE;gBAE9B,GAAG,EAAE,CAAC,MAAW,EAAE,CAAS,EAAE,EAAE,CAAC,CAAC,GAAG,IAAW,EAAE,EAAE;oBAEhD,sEAAsE;oBACtE,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAE3F,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,OAAO,EAAE;wBAEhE,4EAA4E;wBAC5E,MAAM,KAAK,CAAC,wBAAwB,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;qBAClD;oBAED,IAAI;wBAEA,yFAAyF;wBACzF,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;wBAE7I,yDAAyD;wBACzD,OAAO,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;qBAE9D;oBAAC,OAAO,CAAC,EAAE;wBAER,IAAI,CAAC,KAAK,OAAO,EAAE;4BAEf,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;yBAEjB;6BAAM;4BAEH,0GAA0G;4BAC1G,MAAM,CAAC,CAAC;yBACX;qBAEJ;4BAAS;wBAEN,8CAA8C;wBAC9C,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;4BACnC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,MAAM,qCAA6B,CAAC,CAAC,CAAC;yBAC3F;qBACJ;gBACL,CAAC;aACJ,CAAC,CAAC;SACN;QAED,OAAY,IAAI,CAAC,UAAU,CAAC;IAChC,CAAC;IAED;;;;;;;OAOG;IACO,KAAK,CAAC,IAAU;QAEtB,0BAA0B;QAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QAEjB,+CAA+C;QAC/C,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE7B,2BAA2B;QAC3B,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE;YAEX,IAAI;gBAEA,qDAAqD;gBACrD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;aAEvB;YAAC,OAAO,CAAC,EAAE;gBAER,gHAAgH;gBAChH,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,EAAE,EAAE,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC;gBAE5E,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;aAClB;QACL,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAEzB,sCAAsC;QACtC,OAAO,CAAC,CAAC;IACb,CAAC;IASD;;;;OAIG;IACO,sBAAsB;QAC5B,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;YAClC,IAAI,CAAC,SAAS,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC5D;IACL,CAAC;CACJ;AAED;;;;;;;GAOG;AACH,SAAS,WAAW,CAAC,MAA2B,EAAE,MAAc,EAAE,QAAuB,EAAE,WAAgC;IAEvH,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;QACnB,MAAM,KAAK,CAAC,8BAA8B,CAAC,CAAC;KAC/C;IAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;QAE9B,+EAA+E;QAC/E,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;KAE/B;SAAM;QAEH,wEAAwE;QACxE,QAAQ,WAAW,EAAE;YACjB;gBACI,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC9B,MAAM;YACV;gBACI,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACjC,MAAM;YACV;gBACI,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;gBAC1B,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC9B,MAAM;SACb;KACJ;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;AAC1B,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,MAA0B;IAE9D,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,KAAyB,EAAE,GAAW,EAAE,EAAE;QAE7E,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,0CAAkC,CAAC,CAAC,CAAC;QAE/E,OAAO,KAAK,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;AACX,CAAC"}