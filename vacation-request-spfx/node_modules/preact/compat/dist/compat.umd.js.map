{"version": 3, "file": "compat.umd.js", "sources": ["../src/util.js", "../src/PureComponent.js", "../src/memo.js", "../src/forwardRef.js", "../src/Children.js", "../src/suspense.js", "../src/suspense-list.js", "../src/portals.js", "../src/render.js", "../src/index.js"], "sourcesContent": ["/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Check if two objects have a different shape\n * @param {object} a\n * @param {object} b\n * @returns {boolean}\n */\nexport function shallowDiffers(a, b) {\n\tfor (let i in a) if (i !== '__source' && !(i in b)) return true;\n\tfor (let i in b) if (i !== '__source' && a[i] !== b[i]) return true;\n\treturn false;\n}\n\nexport function removeNode(node) {\n\tlet parentNode = node.parentNode;\n\tif (parentNode) parentNode.removeChild(node);\n}\n\n/**\n * Check if two values are the same value\n * @param {*} x\n * @param {*} y\n * @returns {boolean}\n */\nexport function is(x, y) {\n\treturn (x === y && (x !== 0 || 1 / x === 1 / y)) || (x !== x && y !== y);\n}\n", "import { Component } from 'preact';\nimport { shallowDiffers } from './util';\n\n/**\n * Component class with a predefined `shouldComponentUpdate` implementation\n */\nexport function PureComponent(p) {\n\tthis.props = p;\n}\nPureComponent.prototype = new Component();\n// Some third-party libraries check if this property is present\nPureComponent.prototype.isPureReactComponent = true;\nPureComponent.prototype.shouldComponentUpdate = function(props, state) {\n\treturn shallowDiffers(this.props, props) || shallowDiffers(this.state, state);\n};\n", "import { createElement } from 'preact';\nimport { shallowDiffers } from './util';\n\n/**\n * Memoize a component, so that it only updates when the props actually have\n * changed. This was previously known as `React.pure`.\n * @param {import('./internal').FunctionComponent} c functional component\n * @param {(prev: object, next: object) => boolean} [comparer] Custom equality function\n * @returns {import('./internal').FunctionComponent}\n */\nexport function memo(c, comparer) {\n\tfunction shouldUpdate(nextProps) {\n\t\tlet ref = this.props.ref;\n\t\tlet updateRef = ref == nextProps.ref;\n\t\tif (!updateRef && ref) {\n\t\t\tref.call ? ref(null) : (ref.current = null);\n\t\t}\n\n\t\tif (!comparer) {\n\t\t\treturn shallowDiffers(this.props, nextProps);\n\t\t}\n\n\t\treturn !comparer(this.props, nextProps) || !updateRef;\n\t}\n\n\tfunction Memoed(props) {\n\t\tthis.shouldComponentUpdate = shouldUpdate;\n\t\treturn createElement(c, props);\n\t}\n\tMemoed.displayName = 'Memo(' + (c.displayName || c.name) + ')';\n\tMemoed.prototype.isReactComponent = true;\n\tMemoed._forwarded = true;\n\treturn Memoed;\n}\n", "import { options } from 'preact';\nimport { assign } from './util';\n\nlet oldDiffHook = options._diff;\noptions._diff = vnode => {\n\tif (vnode.type && vnode.type._forwarded && vnode.ref) {\n\t\tvnode.props.ref = vnode.ref;\n\t\tvnode.ref = null;\n\t}\n\tif (oldDiffHook) oldDiffHook(vnode);\n};\n\nexport const REACT_FORWARD_SYMBOL =\n\t(typeof Symbol != 'undefined' &&\n\t\tSymbol.for &&\n\t\tSymbol.for('react.forward_ref')) ||\n\t0xf47;\n\n/**\n * Pass ref down to a child. This is mainly used in libraries with HOCs that\n * wrap components. Using `forwardRef` there is an easy way to get a reference\n * of the wrapped component instead of one of the wrapper itself.\n * @param {import('./index').ForwardFn} fn\n * @returns {import('./internal').FunctionComponent}\n */\nexport function forwardRef(fn) {\n\tfunction Forwarded(props) {\n\t\tlet clone = assign({}, props);\n\t\tdelete clone.ref;\n\t\treturn fn(clone, props.ref || null);\n\t}\n\n\t// mobx-react checks for this being present\n\tForwarded.$$typeof = REACT_FORWARD_SYMBOL;\n\t// mobx-react heavily relies on implementation details.\n\t// It expects an object here with a `render` property,\n\t// and prototype.render will fail. Without this\n\t// mobx-react throws.\n\tForwarded.render = Forwarded;\n\n\tForwarded.prototype.isReactComponent = Forwarded._forwarded = true;\n\tForwarded.displayName = 'ForwardRef(' + (fn.displayName || fn.name) + ')';\n\treturn Forwarded;\n}\n", "import { toChildArray } from 'preact';\n\nconst mapFn = (children, fn) => {\n\tif (children == null) return null;\n\treturn toChildArray(toChildArray(children).map(fn));\n};\n\n// This API is completely unnecessary for Preact, so it's basically passthrough.\nexport const Children = {\n\tmap: mapFn,\n\tforEach: mapFn,\n\tcount(children) {\n\t\treturn children ? toChildArray(children).length : 0;\n\t},\n\tonly(children) {\n\t\tconst normalized = toChildArray(children);\n\t\tif (normalized.length !== 1) throw 'Children.only';\n\t\treturn normalized[0];\n\t},\n\ttoArray: toChildArray\n};\n", "import { Component, createElement, options, Fragment } from 'preact';\nimport { assign } from './util';\n\nconst oldCatchError = options._catchError;\noptions._catchError = function(error, newVNode, oldVNode, errorInfo) {\n\tif (error.then) {\n\t\t/** @type {import('./internal').Component} */\n\t\tlet component;\n\t\tlet vnode = newVNode;\n\n\t\tfor (; (vnode = vnode._parent); ) {\n\t\t\tif ((component = vnode._component) && component._childDidSuspend) {\n\t\t\t\tif (newVNode._dom == null) {\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t}\n\t\t\t\t// Don't call oldCatchError if we found a Suspense\n\t\t\t\treturn component._childDidSuspend(error, newVNode);\n\t\t\t}\n\t\t}\n\t}\n\toldCatchError(error, newVNode, oldVNode, errorInfo);\n};\n\nconst oldUnmount = options.unmount;\noptions.unmount = function(vnode) {\n\t/** @type {import('./internal').Component} */\n\tconst component = vnode._component;\n\tif (component && component._onResolve) {\n\t\tcomponent._onResolve();\n\t}\n\n\t// if the component is still hydrating\n\t// most likely it is because the component is suspended\n\t// we set the vnode.type as `null` so that it is not a typeof function\n\t// so the unmount will remove the vnode._dom\n\tif (component && vnode._hydrating === true) {\n\t\tvnode.type = null;\n\t}\n\n\tif (oldUnmount) oldUnmount(vnode);\n};\n\nfunction detachedClone(vnode, detachedParent, parentDom) {\n\tif (vnode) {\n\t\tif (vnode._component && vnode._component.__hooks) {\n\t\t\tvnode._component.__hooks._list.forEach(effect => {\n\t\t\t\tif (typeof effect._cleanup == 'function') effect._cleanup();\n\t\t\t});\n\n\t\t\tvnode._component.__hooks = null;\n\t\t}\n\n\t\tvnode = assign({}, vnode);\n\t\tif (vnode._component != null) {\n\t\t\tif (vnode._component._parentDom === parentDom) {\n\t\t\t\tvnode._component._parentDom = detachedParent;\n\t\t\t}\n\t\t\tvnode._component = null;\n\t\t}\n\n\t\tvnode._children =\n\t\t\tvnode._children &&\n\t\t\tvnode._children.map(child =>\n\t\t\t\tdetachedClone(child, detachedParent, parentDom)\n\t\t\t);\n\t}\n\n\treturn vnode;\n}\n\nfunction removeOriginal(vnode, detachedParent, originalParent) {\n\tif (vnode) {\n\t\tvnode._original = null;\n\t\tvnode._children =\n\t\t\tvnode._children &&\n\t\t\tvnode._children.map(child =>\n\t\t\t\tremoveOriginal(child, detachedParent, originalParent)\n\t\t\t);\n\n\t\tif (vnode._component) {\n\t\t\tif (vnode._component._parentDom === detachedParent) {\n\t\t\t\tif (vnode._dom) {\n\t\t\t\t\toriginalParent.insertBefore(vnode._dom, vnode._nextDom);\n\t\t\t\t}\n\t\t\t\tvnode._component._force = true;\n\t\t\t\tvnode._component._parentDom = originalParent;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn vnode;\n}\n\n// having custom inheritance instead of a class here saves a lot of bytes\nexport function Suspense() {\n\t// we do not call super here to golf some bytes...\n\tthis._pendingSuspensionCount = 0;\n\tthis._suspenders = null;\n\tthis._detachOnNextRender = null;\n}\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspense.prototype = new Component();\n\n/**\n * @this {import('./internal').SuspenseComponent}\n * @param {Promise} promise The thrown promise\n * @param {import('./internal').VNode<any, any>} suspendingVNode The suspending component\n */\nSuspense.prototype._childDidSuspend = function(promise, suspendingVNode) {\n\tconst suspendingComponent = suspendingVNode._component;\n\n\t/** @type {import('./internal').SuspenseComponent} */\n\tconst c = this;\n\n\tif (c._suspenders == null) {\n\t\tc._suspenders = [];\n\t}\n\tc._suspenders.push(suspendingComponent);\n\n\tconst resolve = suspended(c._vnode);\n\n\tlet resolved = false;\n\tconst onResolved = () => {\n\t\tif (resolved) return;\n\n\t\tresolved = true;\n\t\tsuspendingComponent._onResolve = null;\n\n\t\tif (resolve) {\n\t\t\tresolve(onSuspensionComplete);\n\t\t} else {\n\t\t\tonSuspensionComplete();\n\t\t}\n\t};\n\n\tsuspendingComponent._onResolve = onResolved;\n\n\tconst onSuspensionComplete = () => {\n\t\tif (!--c._pendingSuspensionCount) {\n\t\t\t// If the suspension was during hydration we don't need to restore the\n\t\t\t// suspended children into the _children array\n\t\t\tif (c.state._suspended) {\n\t\t\t\tconst suspendedVNode = c.state._suspended;\n\t\t\t\tc._vnode._children[0] = removeOriginal(\n\t\t\t\t\tsuspendedVNode,\n\t\t\t\t\tsuspendedVNode._component._parentDom,\n\t\t\t\t\tsuspendedVNode._component._originalParentDom\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tc.setState({ _suspended: (c._detachOnNextRender = null) });\n\n\t\t\tlet suspended;\n\t\t\twhile ((suspended = c._suspenders.pop())) {\n\t\t\t\tsuspended.forceUpdate();\n\t\t\t}\n\t\t}\n\t};\n\n\t/**\n\t * We do not set `suspended: true` during hydration because we want the actual markup\n\t * to remain on screen and hydrate it when the suspense actually gets resolved.\n\t * While in non-hydration cases the usual fallback -> component flow would occour.\n\t */\n\tconst wasHydrating = suspendingVNode._hydrating === true;\n\tif (!c._pendingSuspensionCount++ && !wasHydrating) {\n\t\tc.setState({ _suspended: (c._detachOnNextRender = c._vnode._children[0]) });\n\t}\n\tpromise.then(onResolved, onResolved);\n};\n\nSuspense.prototype.componentWillUnmount = function() {\n\tthis._suspenders = [];\n};\n\n/**\n * @this {import('./internal').SuspenseComponent}\n * @param {import('./internal').SuspenseComponent[\"props\"]} props\n * @param {import('./internal').SuspenseState} state\n */\nSuspense.prototype.render = function(props, state) {\n\tif (this._detachOnNextRender) {\n\t\t// When the Suspense's _vnode was created by a call to createVNode\n\t\t// (i.e. due to a setState further up in the tree)\n\t\t// it's _children prop is null, in this case we \"forget\" about the parked vnodes to detach\n\t\tif (this._vnode._children) {\n\t\t\tconst detachedParent = document.createElement('div');\n\t\t\tconst detachedComponent = this._vnode._children[0]._component;\n\t\t\tthis._vnode._children[0] = detachedClone(\n\t\t\t\tthis._detachOnNextRender,\n\t\t\t\tdetachedParent,\n\t\t\t\t(detachedComponent._originalParentDom = detachedComponent._parentDom)\n\t\t\t);\n\t\t}\n\n\t\tthis._detachOnNextRender = null;\n\t}\n\n\t// Wrap fallback tree in a VNode that prevents itself from being marked as aborting mid-hydration:\n\t/** @type {import('./internal').VNode} */\n\tconst fallback =\n\t\tstate._suspended && createElement(Fragment, null, props.fallback);\n\tif (fallback) fallback._hydrating = null;\n\n\treturn [\n\t\tcreateElement(Fragment, null, state._suspended ? null : props.children),\n\t\tfallback\n\t];\n};\n\n/**\n * Checks and calls the parent component's _suspended method, passing in the\n * suspended vnode. This is a way for a parent (e.g. SuspenseList) to get notified\n * that one of its children/descendants suspended.\n *\n * The parent MAY return a callback. The callback will get called when the\n * suspension resolves, notifying the parent of the fact.\n * Moreover, the callback gets function `unsuspend` as a parameter. The resolved\n * child descendant will not actually get unsuspended until `unsuspend` gets called.\n * This is a way for the parent to delay unsuspending.\n *\n * If the parent does not return a callback then the resolved vnode\n * gets unsuspended immediately when it resolves.\n *\n * @param {import('./internal').VNode} vnode\n * @returns {((unsuspend: () => void) => void)?}\n */\nexport function suspended(vnode) {\n\t/** @type {import('./internal').Component} */\n\tlet component = vnode._parent._component;\n\treturn component && component._suspended && component._suspended(vnode);\n}\n\nexport function lazy(loader) {\n\tlet prom;\n\tlet component;\n\tlet error;\n\n\tfunction Lazy(props) {\n\t\tif (!prom) {\n\t\t\tprom = loader();\n\t\t\tprom.then(\n\t\t\t\texports => {\n\t\t\t\t\tcomponent = exports.default || exports;\n\t\t\t\t},\n\t\t\t\te => {\n\t\t\t\t\terror = e;\n\t\t\t\t}\n\t\t\t);\n\t\t}\n\n\t\tif (error) {\n\t\t\tthrow error;\n\t\t}\n\n\t\tif (!component) {\n\t\t\tthrow prom;\n\t\t}\n\n\t\treturn createElement(component, props);\n\t}\n\n\tLazy.displayName = 'Lazy';\n\tLazy._forwarded = true;\n\treturn Lazy;\n}\n", "import { Component, toChildArray } from 'preact';\nimport { suspended } from './suspense.js';\n\n// Indexes to linked list nodes (nodes are stored as arrays to save bytes).\nconst SUSPENDED_COUNT = 0;\nconst RESOLVED_COUNT = 1;\nconst NEXT_NODE = 2;\n\n// Having custom inheritance instead of a class here saves a lot of bytes.\nexport function SuspenseList() {\n\tthis._next = null;\n\tthis._map = null;\n}\n\n// Mark one of child's earlier suspensions as resolved.\n// Some pending callbacks may become callable due to this\n// (e.g. the last suspended descendant gets resolved when\n// revealOrder === 'together'). Process those callbacks as well.\nconst resolve = (list, child, node) => {\n\tif (++node[RESOLVED_COUNT] === node[SUSPENDED_COUNT]) {\n\t\t// The number a child (or any of its descendants) has been suspended\n\t\t// matches the number of times it's been resolved. Therefore we\n\t\t// mark the child as completely resolved by deleting it from ._map.\n\t\t// This is used to figure out when *all* children have been completely\n\t\t// resolved when revealOrder is 'together'.\n\t\tlist._map.delete(child);\n\t}\n\n\t// If revealOrder is falsy then we can do an early exit, as the\n\t// callbacks won't get queued in the node anyway.\n\t// If revealOrder is 'together' then also do an early exit\n\t// if all suspended descendants have not yet been resolved.\n\tif (\n\t\t!list.props.revealOrder ||\n\t\t(list.props.revealOrder[0] === 't' && list._map.size)\n\t) {\n\t\treturn;\n\t}\n\n\t// Walk the currently suspended children in order, calling their\n\t// stored callbacks on the way. Stop if we encounter a child that\n\t// has not been completely resolved yet.\n\tnode = list._next;\n\twhile (node) {\n\t\twhile (node.length > 3) {\n\t\t\tnode.pop()();\n\t\t}\n\t\tif (node[RESOLVED_COUNT] < node[SUSPENDED_COUNT]) {\n\t\t\tbreak;\n\t\t}\n\t\tlist._next = node = node[NEXT_NODE];\n\t}\n};\n\n// Things we do here to save some bytes but are not proper JS inheritance:\n// - call `new Component()` as the prototype\n// - do not set `Suspense.prototype.constructor` to `Suspense`\nSuspenseList.prototype = new Component();\n\nSuspenseList.prototype._suspended = function(child) {\n\tconst list = this;\n\tconst delegated = suspended(list._vnode);\n\n\tlet node = list._map.get(child);\n\tnode[SUSPENDED_COUNT]++;\n\n\treturn unsuspend => {\n\t\tconst wrappedUnsuspend = () => {\n\t\t\tif (!list.props.revealOrder) {\n\t\t\t\t// Special case the undefined (falsy) revealOrder, as there\n\t\t\t\t// is no need to coordinate a specific order or unsuspends.\n\t\t\t\tunsuspend();\n\t\t\t} else {\n\t\t\t\tnode.push(unsuspend);\n\t\t\t\tresolve(list, child, node);\n\t\t\t}\n\t\t};\n\t\tif (delegated) {\n\t\t\tdelegated(wrappedUnsuspend);\n\t\t} else {\n\t\t\twrappedUnsuspend();\n\t\t}\n\t};\n};\n\nSuspenseList.prototype.render = function(props) {\n\tthis._next = null;\n\tthis._map = new Map();\n\n\tconst children = toChildArray(props.children);\n\tif (props.revealOrder && props.revealOrder[0] === 'b') {\n\t\t// If order === 'backwards' (or, well, anything starting with a 'b')\n\t\t// then flip the child list around so that the last child will be\n\t\t// the first in the linked list.\n\t\tchildren.reverse();\n\t}\n\t// Build the linked list. Iterate through the children in reverse order\n\t// so that `_next` points to the first linked list node to be resolved.\n\tfor (let i = children.length; i--; ) {\n\t\t// Create a new linked list node as an array of form:\n\t\t// \t[suspended_count, resolved_count, next_node]\n\t\t// where suspended_count and resolved_count are numeric counters for\n\t\t// keeping track how many times a node has been suspended and resolved.\n\t\t//\n\t\t// Note that suspended_count starts from 1 instead of 0, so we can block\n\t\t// processing callbacks until componentDidMount has been called. In a sense\n\t\t// node is suspended at least until componentDidMount gets called!\n\t\t//\n\t\t// Pending callbacks are added to the end of the node:\n\t\t// \t[suspended_count, resolved_count, next_node, callback_0, callback_1, ...]\n\t\tthis._map.set(children[i], (this._next = [1, 0, this._next]));\n\t}\n\treturn props.children;\n};\n\nSuspenseList.prototype.componentDidUpdate = SuspenseList.prototype.componentDidMount = function() {\n\t// Iterate through all children after mounting for two reasons:\n\t// 1. As each node[SUSPENDED_COUNT] starts from 1, this iteration increases\n\t//    each node[RELEASED_COUNT] by 1, therefore balancing the counters.\n\t//    The nodes can now be completely consumed from the linked list.\n\t// 2. Handle nodes that might have gotten resolved between render and\n\t//    componentDidMount.\n\tthis._map.forEach((node, child) => {\n\t\tresolve(this, child, node);\n\t});\n};\n", "import { createElement, render } from 'preact';\n\n/**\n * @param {import('../../src/index').RenderableProps<{ context: any }>} props\n */\nfunction ContextProvider(props) {\n\tthis.getChildContext = () => props.context;\n\treturn props.children;\n}\n\n/**\n * Portal component\n * @this {import('./internal').Component}\n * @param {object | null | undefined} props\n *\n * TODO: use createRoot() instead of fake root\n */\nfunction Portal(props) {\n\tconst _this = this;\n\tlet container = props._container;\n\n\t_this.componentWillUnmount = function() {\n\t\trender(null, _this._temp);\n\t\t_this._temp = null;\n\t\t_this._container = null;\n\t};\n\n\t// When we change container we should clear our old container and\n\t// indicate a new mount.\n\tif (_this._container && _this._container !== container) {\n\t\t_this.componentWillUnmount();\n\t}\n\n\t// When props.vnode is undefined/false/null we are dealing with some kind of\n\t// conditional vnode. This should not trigger a render.\n\tif (props._vnode) {\n\t\tif (!_this._temp) {\n\t\t\t_this._container = container;\n\n\t\t\t// Create a fake DOM parent node that manages a subset of `container`'s children:\n\t\t\t_this._temp = {\n\t\t\t\tnodeType: 1,\n\t\t\t\tparentNode: container,\n\t\t\t\tchildNodes: [],\n\t\t\t\tappendChild(child) {\n\t\t\t\t\tthis.childNodes.push(child);\n\t\t\t\t\t_this._container.appendChild(child);\n\t\t\t\t},\n\t\t\t\tinsertBefore(child, before) {\n\t\t\t\t\tthis.childNodes.push(child);\n\t\t\t\t\t_this._container.appendChild(child);\n\t\t\t\t},\n\t\t\t\tremoveChild(child) {\n\t\t\t\t\tthis.childNodes.splice(this.childNodes.indexOf(child) >>> 1, 1);\n\t\t\t\t\t_this._container.removeChild(child);\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\n\t\t// Render our wrapping element into temp.\n\t\trender(\n\t\t\tcreateElement(ContextProvider, { context: _this.context }, props._vnode),\n\t\t\t_this._temp\n\t\t);\n\t}\n\t// When we come from a conditional render, on a mounted\n\t// portal we should clear the DOM.\n\telse if (_this._temp) {\n\t\t_this.componentWillUnmount();\n\t}\n}\n\n/**\n * Create a `Portal` to continue rendering the vnode tree at a different DOM node\n * @param {import('./internal').VNode} vnode The vnode to render\n * @param {import('./internal').PreactElement} container The DOM node to continue rendering in to.\n */\nexport function createPortal(vnode, container) {\n\tconst el = createElement(Portal, { _vnode: vnode, _container: container });\n\tel.containerInfo = container;\n\treturn el;\n}\n", "import {\n\trender as preactRender,\n\thydrate as preactHydrate,\n\toptions,\n\ttoChildArray,\n\tComponent\n} from 'preact';\n\nexport const REACT_ELEMENT_TYPE =\n\t(typeof Symbol != 'undefined' && Symbol.for && Symbol.for('react.element')) ||\n\t0xeac7;\n\nconst CAMEL_PROPS = /^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/;\n\nconst IS_DOM = typeof document !== 'undefined';\n\n// Input types for which onchange should not be converted to oninput.\n// type=\"file|checkbox|radio\", plus \"range\" in IE11.\n// (IE11 doesn't support Symbol, which we use here to turn `rad` into `ra` which matches \"range\")\nconst onChangeInputType = type =>\n\t(typeof Symbol != 'undefined' && typeof Symbol() == 'symbol'\n\t\t? /fil|che|rad/i\n\t\t: /fil|che|ra/i\n\t).test(type);\n\n// Some libraries like `react-virtualized` explicitly check for this.\nComponent.prototype.isReactComponent = {};\n\n// `UNSAFE_*` lifecycle hooks\n// Preact only ever invokes the unprefixed methods.\n// Here we provide a base \"fallback\" implementation that calls any defined UNSAFE_ prefixed method.\n// - If a component defines its own `componentDidMount()` (including via defineProperty), use that.\n// - If a component defines `UNSAFE_componentDidMount()`, `componentDidMount` is the alias getter/setter.\n// - If anything assigns to an `UNSAFE_*` property, the assignment is forwarded to the unprefixed property.\n// See https://github.com/preactjs/preact/issues/1941\n[\n\t'componentWillMount',\n\t'componentWillReceiveProps',\n\t'componentWillUpdate'\n].forEach(key => {\n\tObject.defineProperty(Component.prototype, key, {\n\t\tconfigurable: true,\n\t\tget() {\n\t\t\treturn this['UNSAFE_' + key];\n\t\t},\n\t\tset(v) {\n\t\t\tObject.defineProperty(this, key, {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true,\n\t\t\t\tvalue: v\n\t\t\t});\n\t\t}\n\t});\n});\n\n/**\n * Proxy render() since React returns a Component reference.\n * @param {import('./internal').VNode} vnode VNode tree to render\n * @param {import('./internal').PreactElement} parent DOM node to render vnode tree into\n * @param {() => void} [callback] Optional callback that will be called after rendering\n * @returns {import('./internal').Component | null} The root component reference or null\n */\nexport function render(vnode, parent, callback) {\n\t// React destroys any existing DOM nodes, see #1727\n\t// ...but only on the first render, see #1828\n\tif (parent._children == null) {\n\t\tparent.textContent = '';\n\t}\n\n\tpreactRender(vnode, parent);\n\tif (typeof callback == 'function') callback();\n\n\treturn vnode ? vnode._component : null;\n}\n\nexport function hydrate(vnode, parent, callback) {\n\tpreactHydrate(vnode, parent);\n\tif (typeof callback == 'function') callback();\n\n\treturn vnode ? vnode._component : null;\n}\n\nlet oldEventHook = options.event;\noptions.event = e => {\n\tif (oldEventHook) e = oldEventHook(e);\n\te.persist = empty;\n\te.isPropagationStopped = isPropagationStopped;\n\te.isDefaultPrevented = isDefaultPrevented;\n\treturn (e.nativeEvent = e);\n};\n\nfunction empty() {}\n\nfunction isPropagationStopped() {\n\treturn this.cancelBubble;\n}\n\nfunction isDefaultPrevented() {\n\treturn this.defaultPrevented;\n}\n\nlet classNameDescriptor = {\n\tconfigurable: true,\n\tget() {\n\t\treturn this.class;\n\t}\n};\n\nlet oldVNodeHook = options.vnode;\noptions.vnode = vnode => {\n\tlet type = vnode.type;\n\tlet props = vnode.props;\n\tlet normalizedProps = props;\n\n\t// only normalize props on Element nodes\n\tif (typeof type === 'string') {\n\t\tconst nonCustomElement = type.indexOf('-') === -1;\n\t\tnormalizedProps = {};\n\n\t\tfor (let i in props) {\n\t\t\tlet value = props[i];\n\n\t\t\tif (IS_DOM && i === 'children' && type === 'noscript') {\n\t\t\t\t// Emulate React's behavior of not rendering the contents of noscript tags on the client.\n\t\t\t\tcontinue;\n\t\t\t} else if (i === 'value' && 'defaultValue' in props && value == null) {\n\t\t\t\t// Skip applying value if it is null/undefined and we already set\n\t\t\t\t// a default value\n\t\t\t\tcontinue;\n\t\t\t} else if (\n\t\t\t\ti === 'defaultValue' &&\n\t\t\t\t'value' in props &&\n\t\t\t\tprops.value == null\n\t\t\t) {\n\t\t\t\t// `defaultValue` is treated as a fallback `value` when a value prop is present but null/undefined.\n\t\t\t\t// `defaultValue` for Elements with no value prop is the same as the DOM defaultValue property.\n\t\t\t\ti = 'value';\n\t\t\t} else if (i === 'download' && value === true) {\n\t\t\t\t// Calling `setAttribute` with a truthy value will lead to it being\n\t\t\t\t// passed as a stringified value, e.g. `download=\"true\"`. React\n\t\t\t\t// converts it to an empty string instead, otherwise the attribute\n\t\t\t\t// value will be used as the file name and the file will be called\n\t\t\t\t// \"true\" upon downloading it.\n\t\t\t\tvalue = '';\n\t\t\t} else if (/ondoubleclick/i.test(i)) {\n\t\t\t\ti = 'ondblclick';\n\t\t\t} else if (\n\t\t\t\t/^onchange(textarea|input)/i.test(i + type) &&\n\t\t\t\t!onChangeInputType(props.type)\n\t\t\t) {\n\t\t\t\ti = 'oninput';\n\t\t\t} else if (/^onfocus$/i.test(i)) {\n\t\t\t\ti = 'onfocusin';\n\t\t\t} else if (/^onblur$/i.test(i)) {\n\t\t\t\ti = 'onfocusout';\n\t\t\t} else if (/^on(Ani|Tra|Tou|BeforeInp|Compo)/.test(i)) {\n\t\t\t\ti = i.toLowerCase();\n\t\t\t} else if (nonCustomElement && CAMEL_PROPS.test(i)) {\n\t\t\t\ti = i.replace(/[A-Z0-9]/g, '-$&').toLowerCase();\n\t\t\t} else if (value === null) {\n\t\t\t\tvalue = undefined;\n\t\t\t}\n\n\t\t\t// Add support for onInput and onChange, see #3561\n\t\t\t// if we have an oninput prop already change it to oninputCapture\n\t\t\tif (/^oninput$/i.test(i)) {\n\t\t\t\ti = i.toLowerCase();\n\t\t\t\tif (normalizedProps[i]) {\n\t\t\t\t\ti = 'oninputCapture';\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tnormalizedProps[i] = value;\n\t\t}\n\n\t\t// Add support for array select values: <select multiple value={[]} />\n\t\tif (\n\t\t\ttype == 'select' &&\n\t\t\tnormalizedProps.multiple &&\n\t\t\tArray.isArray(normalizedProps.value)\n\t\t) {\n\t\t\t// forEach() always returns undefined, which we abuse here to unset the value prop.\n\t\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\n\t\t\t\tchild.props.selected =\n\t\t\t\t\tnormalizedProps.value.indexOf(child.props.value) != -1;\n\t\t\t});\n\t\t}\n\n\t\t// Adding support for defaultValue in select tag\n\t\tif (type == 'select' && normalizedProps.defaultValue != null) {\n\t\t\tnormalizedProps.value = toChildArray(props.children).forEach(child => {\n\t\t\t\tif (normalizedProps.multiple) {\n\t\t\t\t\tchild.props.selected =\n\t\t\t\t\t\tnormalizedProps.defaultValue.indexOf(child.props.value) != -1;\n\t\t\t\t} else {\n\t\t\t\t\tchild.props.selected =\n\t\t\t\t\t\tnormalizedProps.defaultValue == child.props.value;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tvnode.props = normalizedProps;\n\n\t\tif (props.class != props.className) {\n\t\t\tclassNameDescriptor.enumerable = 'className' in props;\n\t\t\tif (props.className != null) normalizedProps.class = props.className;\n\t\t\tObject.defineProperty(normalizedProps, 'className', classNameDescriptor);\n\t\t}\n\t}\n\n\tvnode.$$typeof = REACT_ELEMENT_TYPE;\n\n\tif (oldVNodeHook) oldVNodeHook(vnode);\n};\n\n// Only needed for react-relay\nlet currentComponent;\nconst oldBeforeRender = options._render;\noptions._render = function(vnode) {\n\tif (oldBeforeRender) {\n\t\toldBeforeRender(vnode);\n\t}\n\tcurrentComponent = vnode._component;\n};\n\n// This is a very very private internal function for React it\n// is used to sort-of do runtime dependency injection. So far\n// only `react-relay` makes use of it. It uses it to read the\n// context value.\nexport const __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = {\n\tReactCurrentDispatcher: {\n\t\tcurrent: {\n\t\t\treadContext(context) {\n\t\t\t\treturn currentComponent._globalContext[context._id].props.value;\n\t\t\t}\n\t\t}\n\t}\n};\n", "import {\n\tcreateElement,\n\trender as preactRender,\n\tcloneElement as preactCloneElement,\n\tcreateRef,\n\tComponent,\n\tcreateContext,\n\tFragment\n} from 'preact';\nimport {\n\tuseState,\n\tuseId,\n\tuseReducer,\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseRef,\n\tuseImperativeHandle,\n\tuseMemo,\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue\n} from 'preact/hooks';\nimport { PureComponent } from './PureComponent';\nimport { memo } from './memo';\nimport { forwardRef } from './forwardRef';\nimport { Children } from './Children';\nimport { Suspense, lazy } from './suspense';\nimport { SuspenseList } from './suspense-list';\nimport { createPortal } from './portals';\nimport { is } from './util';\nimport {\n\thydrate,\n\trender,\n\tREACT_ELEMENT_TYPE,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n} from './render';\n\nconst version = '17.0.2'; // trick libraries to think we are react\n\n/**\n * Legacy version of createElement.\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component constructor\n */\nfunction createFactory(type) {\n\treturn createElement.bind(null, type);\n}\n\n/**\n * Check if the passed element is a valid (p)react node.\n * @param {*} element The element to check\n * @returns {boolean}\n */\nfunction isValidElement(element) {\n\treturn !!element && element.$$typeof === REACT_ELEMENT_TYPE;\n}\n\n/**\n * Wrap `cloneElement` to abort if the passed element is not a valid element and apply\n * all vnode normalizations.\n * @param {import('./internal').VNode} element The vnode to clone\n * @param {object} props Props to add when cloning\n * @param {Array<import('./internal').ComponentChildren>} rest Optional component children\n */\nfunction cloneElement(element) {\n\tif (!isValidElement(element)) return element;\n\treturn preactCloneElement.apply(null, arguments);\n}\n\n/**\n * Remove a component tree from the DOM, including state and event handlers.\n * @param {import('./internal').PreactElement} container\n * @returns {boolean}\n */\nfunction unmountComponentAtNode(container) {\n\tif (container._children) {\n\t\tpreactRender(null, container);\n\t\treturn true;\n\t}\n\treturn false;\n}\n\n/**\n * Get the matching DOM node for a component\n * @param {import('./internal').Component} component\n * @returns {import('./internal').PreactElement | null}\n */\nfunction findDOMNode(component) {\n\treturn (\n\t\t(component &&\n\t\t\t(component.base || (component.nodeType === 1 && component))) ||\n\t\tnull\n\t);\n}\n\n/**\n * Deprecated way to control batched rendering inside the reconciler, but we\n * already schedule in batches inside our rendering code\n * @template Arg\n * @param {(arg: Arg) => void} callback function that triggers the updated\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n */\n// eslint-disable-next-line camelcase\nconst unstable_batchedUpdates = (callback, arg) => callback(arg);\n\n/**\n * In React, `flushSync` flushes the entire tree and forces a rerender. It's\n * implmented here as a no-op.\n * @template Arg\n * @template Result\n * @param {(arg: Arg) => Result} callback function that runs before the flush\n * @param {Arg} [arg] Optional argument that can be passed to the callback\n * @returns\n */\nconst flushSync = (callback, arg) => callback(arg);\n\n/**\n * Strict Mode is not implemented in Preact, so we provide a stand-in for it\n * that just renders its children without imposing any restrictions.\n */\nconst StrictMode = Fragment;\n\nexport function startTransition(cb) {\n\tcb();\n}\n\nexport function useDeferredValue(val) {\n\treturn val;\n}\n\nexport function useTransition() {\n\treturn [false, startTransition];\n}\n\n// TODO: in theory this should be done after a VNode is diffed as we want to insert\n// styles/... before it attaches\nexport const useInsertionEffect = useLayoutEffect;\n\n/**\n * This is taken from https://github.com/facebook/react/blob/main/packages/use-sync-external-store/src/useSyncExternalStoreShimClient.js#L84\n * on a high level this cuts out the warnings, ... and attempts a smaller implementation\n */\nexport function useSyncExternalStore(subscribe, getSnapshot) {\n\tconst value = getSnapshot();\n\n\tconst [{ _instance }, forceUpdate] = useState({\n\t\t_instance: { _value: value, _getSnapshot: getSnapshot }\n\t});\n\n\tuseLayoutEffect(() => {\n\t\t_instance._value = value;\n\t\t_instance._getSnapshot = getSnapshot;\n\n\t\tif (!is(_instance._value, getSnapshot())) {\n\t\t\tforceUpdate({ _instance });\n\t\t}\n\t}, [subscribe, value, getSnapshot]);\n\n\tuseEffect(() => {\n\t\tif (!is(_instance._value, _instance._getSnapshot())) {\n\t\t\tforceUpdate({ _instance });\n\t\t}\n\n\t\treturn subscribe(() => {\n\t\t\tif (!is(_instance._value, _instance._getSnapshot())) {\n\t\t\t\tforceUpdate({ _instance });\n\t\t\t}\n\t\t});\n\t}, [subscribe]);\n\n\treturn value;\n}\n\nexport * from 'preact/hooks';\nexport {\n\tversion,\n\tChildren,\n\trender,\n\thydrate,\n\tunmountComponentAtNode,\n\tcreatePortal,\n\tcreateElement,\n\tcreateContext,\n\tcreateFactory,\n\tcloneElement,\n\tcreateRef,\n\tFragment,\n\tisValidElement,\n\tfindDOMNode,\n\tComponent,\n\tPureComponent,\n\tmemo,\n\tforwardRef,\n\tflushSync,\n\t// eslint-disable-next-line camelcase\n\tunstable_batchedUpdates,\n\tStrictMode,\n\tSuspense,\n\tSuspenseList,\n\tlazy,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n};\n\n// React copies the named exports to the default one.\nexport default {\n\tuseState,\n\tuseId,\n\tuseReducer,\n\tuseEffect,\n\tuseLayoutEffect,\n\tuseInsertionEffect,\n\tuseTransition,\n\tuseDeferredValue,\n\tuseSyncExternalStore,\n\tstartTransition,\n\tuseRef,\n\tuseImperativeHandle,\n\tuseMemo,\n\tuseCallback,\n\tuseContext,\n\tuseDebugValue,\n\tversion,\n\tChildren,\n\trender,\n\thydrate,\n\tunmountComponentAtNode,\n\tcreatePortal,\n\tcreateElement,\n\tcreateContext,\n\tcreateFactory,\n\tcloneElement,\n\tcreateRef,\n\tFragment,\n\tisValidElement,\n\tfindDOMNode,\n\tComponent,\n\tPureComponent,\n\tmemo,\n\tforwardRef,\n\tflushSync,\n\tunstable_batchedUpdates,\n\tStrictMode,\n\tSuspense,\n\tSuspenseList,\n\tlazy,\n\t__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED\n};\n"], "names": ["assign", "obj", "props", "i", "shallow<PERSON>iffers", "a", "b", "is", "x", "y", "PureComponent", "p", "this", "memo", "c", "comparer", "shouldUpdate", "nextProps", "ref", "updateRef", "call", "current", "Memoed", "shouldComponentUpdate", "createElement", "displayName", "name", "prototype", "isReactComponent", "Component", "isPureReactComponent", "state", "oldDiffHook", "options", "__b", "vnode", "type", "__f", "REACT_FORWARD_SYMBOL", "Symbol", "for", "forwardRef", "fn", "Forwarded", "clone", "$$typeof", "render", "mapFn", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "Children", "for<PERSON>ach", "count", "length", "only", "normalized", "toArray", "oldCatchError", "__e", "error", "newVNode", "oldVNode", "errorInfo", "then", "component", "__c", "__k", "oldUnmount", "unmount", "detachedClone", "detachedParent", "parentDom", "effect", "__H", "__P", "child", "removeOriginal", "originalParent", "__v", "insertBefore", "__d", "Suspense", "__u", "_suspenders", "suspended", "__", "__a", "lazy", "loader", "prom", "Lazy", "exports", "default", "e", "SuspenseList", "_next", "_map", "__R", "__h", "promise", "suspendingVNode", "suspendingComponent", "push", "resolve", "resolved", "onResolved", "onSuspensionComplete", "suspendedVNode", "setState", "pop", "forceUpdate", "wasHydrating", "componentWillUnmount", "document", "detachedComponent", "__O", "fallback", "Fragment", "list", "node", "delete", "revealOrder", "size", "ContextProvider", "getChildContext", "context", "Portal", "_this", "container", "_container", "_temp", "nodeType", "parentNode", "childNodes", "append<PERSON><PERSON><PERSON>", "before", "<PERSON><PERSON><PERSON><PERSON>", "splice", "indexOf", "createPortal", "el", "containerInfo", "delegated", "get", "unsuspend", "wrappedUnsuspend", "Map", "reverse", "set", "componentDidUpdate", "componentDidMount", "REACT_ELEMENT_TYPE", "CAMEL_PROPS", "IS_DOM", "onChangeInputType", "test", "parent", "callback", "textContent", "preactRender", "hydrate", "preactHydrate", "key", "Object", "defineProperty", "configurable", "v", "writable", "value", "oldEventHook", "event", "empty", "isPropagationStopped", "cancelBubble", "isDefaultPrevented", "defaultPrevented", "persist", "nativeEvent", "currentComponent", "classNameDescriptor", "class", "oldVNodeHook", "normalizedProps", "nonCustomElement", "toLowerCase", "replace", "undefined", "multiple", "Array", "isArray", "selected", "defaultValue", "className", "enumerable", "oldBeforeRender", "__r", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "ReactCur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readContext", "__n", "version", "createFactory", "bind", "isValidElement", "element", "cloneElement", "preactCloneElement", "apply", "arguments", "unmountComponentAtNode", "findDOMNode", "base", "unstable_batchedUpdates", "arg", "flushSync", "StrictMode", "startTransition", "cb", "useDeferredValue", "val", "useTransition", "useInsertionEffect", "useLayoutEffect", "useSyncExternalStore", "subscribe", "getSnapshot", "_useState", "useState", "_instance", "_getSnapshot", "useEffect", "index", "useId", "useReducer", "useRef", "useImperativeHandle", "useMemo", "useCallback", "useContext", "useDebugValue", "createContext", "createRef"], "mappings": "mUAOgBA,SAAAA,EAAOC,EAAKC,GAC3B,IAAK,IAAIC,KAAKD,EAAOD,EAAIE,GAAKD,EAAMC,GACpC,OAA6BF,CAC7B,CAQeG,SAAAA,EAAeC,EAAGC,GACjC,IAAK,IAAIH,KAAKE,EAAG,GAAU,aAANF,KAAsBA,KAAKG,GAAI,OAAO,EAC3D,IAAK,IAAIH,KAAKG,EAAG,GAAU,aAANH,GAAoBE,EAAEF,KAAOG,EAAEH,GAAI,OAAxD,EACA,OAAO,CACP,CAaM,SAASI,EAAGC,EAAGC,GACrB,OAAQD,IAAMC,IAAY,IAAND,GAAW,EAAIA,GAAM,EAAIC,IAAQD,GAAMA,GAAKC,GAAMA,CACtE,CC/BeC,SAAAA,EAAcC,GAC7BC,KAAKV,MAAQS,CACb,CCEM,SAASE,EAAKC,EAAGC,GACvB,SAASC,EAAaC,GACrB,IAAIC,EAAMN,KAAKV,MAAMgB,IACjBC,EAAYD,GAAOD,EAAUC,IAKjC,OAJKC,GAAaD,IACjBA,EAAIE,KAAOF,EAAI,MAASA,EAAIG,QAAU,MAGlCN,GAIGA,EAASH,KAAKV,MAAOe,KAAeE,EAHpCf,EAAeQ,KAAKV,MAAOe,EAInC,CAED,SAASK,EAAOpB,GAEf,OADAU,KAAKW,sBAAwBP,EACtBQ,EAAAA,cAAcV,EAAGZ,EACxB,CAID,OAHAoB,EAAOG,YAAc,SAAWX,EAAEW,aAAeX,EAAEY,MAAQ,IAC3DJ,EAAOK,UAAUC,kBAAmB,EACpCN,OAAoB,EACbA,CACP,EDxBDZ,EAAciB,UAAY,IAAIE,EAA9BA,WAEwBC,sBAAuB,EAC/CpB,EAAciB,UAAUJ,sBAAwB,SAASrB,EAAO6B,GAC/D,OAAO3B,EAAeQ,KAAKV,MAAOA,IAAUE,EAAeQ,KAAKmB,MAAOA,EACvE,EEXD,IAAIC,EAAcC,EAAAA,QAAlBC,IACAD,EAAOA,QAAPC,IAAgB,SAAAC,GACXA,EAAMC,MAAQD,EAAMC,KAApBC,KAAuCF,EAAMjB,MAChDiB,EAAMjC,MAAMgB,IAAMiB,EAAMjB,IACxBiB,EAAMjB,IAAM,MAETc,GAAaA,EAAYG,EAC7B,EAEYG,IAAAA,EACM,oBAAVC,QACPA,OAAOC,KACPD,OAAOC,IAAI,sBACZ,cASeC,EAAWC,GAC1B,SAASC,EAAUzC,GAClB,IAAI0C,EAAQ5C,EAAO,CAAD,EAAKE,GAEvB,cADO0C,EAAM1B,IACNwB,EAAGE,EAAO1C,EAAMgB,KAAO,KAC9B,CAYD,OATAyB,EAAUE,SAAWP,EAKrBK,EAAUG,OAASH,EAEnBA,EAAUhB,UAAUC,iBAAmBe,EAASN,KAAc,EAC9DM,EAAUlB,YAAc,eAAiBiB,EAAGjB,aAAeiB,EAAGhB,MAAQ,IAC/DiB,CACP,CCzCD,IAAMI,EAAQ,SAACC,EAAUN,GACxB,OAAgB,MAAZM,EAAyB,KACtBC,EAAAA,aAAaA,EAAAA,aAAaD,GAAUE,IAAIR,GAC/C,EAGYS,EAAW,CACvBD,IAAKH,EACLK,QAASL,EACTM,MAHuB,SAGjBL,GACL,OAAOA,EAAWC,eAAaD,GAAUM,OAAS,CAClD,EACDC,cAAKP,GACJ,IAAMQ,EAAaP,EAAYA,aAACD,GAChC,GAA0B,IAAtBQ,EAAWF,OAAc,KAAM,gBACnC,OAAOE,EAAW,EAClB,EACDC,QAASR,EAXcA,cCLlBS,EAAgBzB,EAAAA,QAAH0B,IACnB1B,EAAAA,QAAA0B,IAAsB,SAASC,EAAOC,EAAUC,EAAUC,GACzD,GAAIH,EAAMI,KAKT,IAHA,IAAIC,EACA9B,EAAQ0B,EAEJ1B,EAAQA,MACf,IAAK8B,EAAY9B,EAAb+B,MAAkCD,EAAtCC,IAMC,OALqB,MAAjBL,EAAQF,MACXE,EAAAF,IAAgBG,EAChBD,IAAAA,EAAAM,IAAqBL,EAArBK,KAGMF,EAASC,IAAkBN,EAAOC,GAI5CH,EAAcE,EAAOC,EAAUC,EAAUC,EACzC,EAED,IAAMK,EAAanC,EAAOA,QAACoC,QAmB3B,SAASC,EAAcnC,EAAOoC,EAAgBC,GAyB7C,OAxBIrC,IACCA,EAAK+B,KAAe/B,YACvBA,EAAK+B,IAA0Bd,IAAAA,GAAAA,QAAQ,SAAAqB,GACR,mBAAnBA,EAAPP,KAAsCO,EAAMP,KAChD,GAED/B,EAAK+B,IAAsBQ,IAAA,MAIJ,OADxBvC,EAAQnC,EAAO,CAAD,EAAKmC,IACV+B,MACJ/B,EAAK+B,UAA2BM,IACnCrC,EAAA+B,IAAAS,IAA8BJ,GAE/BpC,MAAmB,MAGpBA,EAAKgC,IACJhC,EAAAgC,KACAhC,EAAAgC,IAAgBjB,IAAI,SAAA0B,UACnBN,EAAcM,EAAOL,EAAgBC,EADb,IAKpBrC,CACP,CAED,SAAS0C,EAAe1C,EAAOoC,EAAgBO,GAoB9C,OAnBI3C,IACHA,EAAK4C,IAAa,KAClB5C,EAAKgC,IACJhC,OACAA,EAAAgC,IAAgBjB,IAAI,SAAA0B,GAAK,OACxBC,EAAeD,EAAOL,EAAgBO,EADd,GAItB3C,OACCA,EAAA+B,IAAAS,MAAgCJ,IAC/BpC,EAAYwB,KACfmB,EAAeE,aAAa7C,EAAYA,IAAAA,EACxC8C,KACD9C,EAAK+B,SAAqB,EAC1B/B,EAAK+B,IAAyBY,IAAAA,IAK1B3C,CACP,CAGe+C,SAAAA,IAEftE,KAAAuE,IAA+B,EAC/BvE,KAAKwE,EAAc,KACnBxE,SAA2B,IAC3B,CAmIM,SAASyE,EAAUlD,GAEzB,IAAI8B,EAAY9B,EAAHmD,GAAApB,IACb,OAAOD,GAAaA,EAAJsB,KAA4BtB,MAAqB9B,EACjE,UAEeqD,EAAKC,GACpB,IAAIC,EACAzB,EACAL,EAEJ,SAAS+B,EAAKzF,GAab,GAZKwF,IACJA,EAAOD,KACFzB,KACJ,SAAA4B,GACC3B,EAAY2B,EAAQC,SAAWD,CAC/B,EACD,SAAAE,GACClC,EAAQkC,CACR,GAIClC,EACH,MAAMA,EAGP,IAAKK,EACJ,MAAMyB,EAGP,OAAOlE,EAAaA,cAACyC,EAAW/D,EAChC,CAID,OAFAyF,EAAKlE,YAAc,OACnBkE,EAAItD,KAAc,EACXsD,CACP,CCpQeI,SAAAA,IACfnF,KAAKoF,EAAQ,KACbpF,KAAKqF,EAAO,IACZ,CDaDhE,EAAOA,QAACoC,QAAU,SAASlC,GAE1B,IAAM8B,EAAY9B,EAAlB+B,IACID,GAAaA,EAAJiC,KACZjC,EAAAiC,MAOGjC,IAAkC,IAArB9B,EAAAgE,MAChBhE,EAAMC,KAAO,MAGVgC,GAAYA,EAAWjC,EAC3B,GAgED+C,EAASvD,UAAY,IAAIE,aAOaqC,IAAA,SAASkC,EAASC,GACvD,IAAMC,EAAsBD,EAAHnC,IAGnBpD,EAAIF,KAEW,MAAjBE,EAAEsE,IACLtE,EAAEsE,EAAc,IAEjBtE,EAAEsE,EAAYmB,KAAKD,GAEnB,IAAME,EAAUnB,EAAUvE,EAADiE,KAErB0B,GAAW,EACTC,EAAa,WACdD,IAEJA,GAAW,EACXH,EAAAJ,IAAiC,KAE7BM,EACHA,EAAQG,GAERA,IAED,EAEDL,EAAAJ,IAAiCQ,EAEjC,IAAMC,EAAuB,WAC5B,MAAO7F,EAAPqE,IAAkC,CAGjC,GAAIrE,EAAEiB,MAAkBwD,IAAA,CACvB,IAAMqB,EAAiB9F,EAAEiB,UACzBjB,EAAAiE,IAAAZ,IAAmB,GAAKU,EACvB+B,EACAA,EACAA,IAAAA,IAAAA,UAED,CAID,IAAIvB,EACJ,IAHAvE,EAAE+F,SAAS,CAAEtB,IAAazE,EAACoB,IAAuB,OAG1CmD,EAAYvE,EAAEsE,EAAY0B,OACjCzB,EAAU0B,aAEX,CACD,EAOKC,GAA8C,IAA/BX,EAAAF,IAChBrF,EAAAqE,OAAgC6B,GACpClG,EAAE+F,SAAS,CAAEtB,IAAazE,EAAAoB,IAAwBpB,EAAAiE,IAAAZ,IAAmB,KAEtEiC,EAAQpC,KAAK0C,EAAYA,EACzB,EAEDxB,EAASvD,UAAUsF,qBAAuB,WACzCrG,KAAKwE,EAAc,EACnB,EAODF,EAASvD,UAAUmB,OAAS,SAAS5C,EAAO6B,GAC3C,GAAInB,KAA0BsB,IAAA,CAI7B,GAAItB,KAAuBmE,IAAAZ,IAAA,CAC1B,IAAMI,EAAiB2C,SAAS1F,cAAc,OACxC2F,EAAoBvG,KAAAmE,IAAAZ,IAAsB,GAAhDD,IACAtD,aAAsB,GAAK0D,EAC1B1D,KADuCsB,IAEvCqC,EACC4C,EAAAC,IAAuCD,EAAvCxC,IAEF,CAED/D,KAAAsB,IAA2B,IAC3B,CAID,IAAMmF,EACLtF,EAAAwD,KAAoB/D,EAAaA,cAAC8F,WAAU,KAAMpH,EAAMmH,UAGzD,OAFIA,IAAUA,MAAsB,MAE7B,CACN7F,EAAaA,cAAC8F,EAADA,SAAW,KAAMvF,EAAKwD,IAAc,KAAOrF,EAAM8C,UAC9DqE,EAED,EClMD,IAAMb,EAAU,SAACe,EAAM3C,EAAO4C,GAc7B,KAbMA,EAdgB,KAcSA,EAfR,IAqBtBD,EAAKtB,EAAKwB,OAAO7C,GAQhB2C,EAAKrH,MAAMwH,cACmB,MAA9BH,EAAKrH,MAAMwH,YAAY,KAAcH,EAAKtB,EAAK0B,MASjD,IADAH,EAAOD,EAAKvB,EACLwB,GAAM,CACZ,KAAOA,EAAKlE,OAAS,GACpBkE,EAAKV,KAALU,GAED,GAAIA,EA1CiB,GA0CMA,EA3CL,GA4CrB,MAEDD,EAAKvB,EAAQwB,EAAOA,EA5CJ,EA6ChB,CACD,EC/CD,SAASI,EAAgB1H,GAExB,OADAU,KAAKiH,gBAAkB,WAAA,OAAM3H,EAAM4H,OAAZ,EAChB5H,EAAM8C,QACb,CASD,SAAS+E,EAAO7H,GACf,IAAM8H,EAAQpH,KACVqH,EAAY/H,EAAMgI,EAEtBF,EAAMf,qBAAuB,WAC5BnE,EAAAA,OAAO,KAAMkF,EAAMG,GACnBH,EAAMG,EAAQ,KACdH,EAAME,EAAa,IACnB,EAIGF,EAAME,GAAcF,EAAME,IAAeD,GAC5CD,EAAMf,uBAKH/G,EAAJ6E,KACMiD,EAAMG,IACVH,EAAME,EAAaD,EAGnBD,EAAMG,EAAQ,CACbC,SAAU,EACVC,WAAYJ,EACZK,WAAY,GACZC,YAAY3D,SAAAA,GACXhE,KAAK0H,WAAW/B,KAAK3B,GACrBoD,EAAME,EAAWK,YAAY3D,EAC7B,EACDI,aARa,SAQAJ,EAAO4D,GACnB5H,KAAK0H,WAAW/B,KAAK3B,GACrBoD,EAAME,EAAWK,YAAY3D,EAC7B,EACD6D,YAAY7D,SAAAA,GACXhE,KAAK0H,WAAWI,OAAO9H,KAAK0H,WAAWK,QAAQ/D,KAAW,EAAG,GAC7DoD,EAAME,EAAWO,YAAY7D,EAC7B,IAKH9B,EAAAA,OACCtB,EAAAA,cAAcoG,EAAiB,CAAEE,QAASE,EAAMF,SAAW5H,EAA9C6E,KACbiD,EAAMG,IAKCH,EAAMG,GACdH,EAAMf,sBAEP,CAOM,SAAS2B,EAAazG,EAAO8F,GACnC,IAAMY,EAAKrH,EAAaA,cAACuG,EAAQ,CAAEhD,IAAQ5C,EAAO+F,EAAYD,IAE9D,OADAY,EAAGC,cAAgBb,EACZY,CACP,EDxBD9C,EAAapE,UAAY,IAAIE,aAEO0D,IAAA,SAASX,GAC5C,IAAM2C,EAAO3G,KACPmI,EAAY1D,EAAUkC,EAA5BxC,KAEIyC,EAAOD,EAAKtB,EAAK+C,IAAIpE,GAGzB,OAFA4C,EA5DuB,cA8DhByB,GACN,IAAMC,EAAmB,WACnB3B,EAAKrH,MAAMwH,aAKfF,EAAKjB,KAAK0C,GACVzC,EAAQe,EAAM3C,EAAO4C,IAHrByB,GAKD,EACGF,EACHA,EAAUG,GAEVA,GAED,CACD,EAEDnD,EAAapE,UAAUmB,OAAS,SAAS5C,GACxCU,KAAKoF,EAAQ,KACbpF,KAAKqF,EAAO,IAAIkD,IAEhB,IAAMnG,EAAWC,EAAAA,aAAa/C,EAAM8C,UAChC9C,EAAMwH,aAAwC,MAAzBxH,EAAMwH,YAAY,IAI1C1E,EAASoG,UAIV,IAAK,IAAIjJ,EAAI6C,EAASM,OAAQnD,KAY7BS,KAAKqF,EAAKoD,IAAIrG,EAAS7C,GAAKS,KAAKoF,EAAQ,CAAC,EAAG,EAAGpF,KAAKoF,IAEtD,OAAO9F,EAAM8C,QACb,EAED+C,EAAapE,UAAU2H,mBAAqBvD,EAAapE,UAAU4H,kBAAoB,WAAW,IAAAvB,EAAApH,KAOjGA,KAAKqF,EAAK7C,QAAQ,SAACoE,EAAM5C,GACxB4B,EAAQwB,EAAMpD,EAAO4C,EACrB,EACD,MErHYgC,EACM,oBAAVjH,QAAyBA,OAAOC,KAAOD,OAAOC,IAAI,kBAC1D,MAEKiH,EAAc,0RAEdC,EAA6B,oBAAbxC,SAKhByC,EAAoB,SAAAvH,GACzB,OAAkB,oBAAVG,QAA4C,iBAAZA,SACrC,eACA,eACDqH,KAAKxH,EAJsB,EA2CvB,SAASU,EAAOX,EAAO0H,EAAQC,GAUrC,OAPwB,MAApBD,EAAA1F,MACH0F,EAAOE,YAAc,IAGtBC,EAAAA,OAAa7H,EAAO0H,GACG,mBAAZC,GAAwBA,IAE5B3H,EAAQA,EAAmB+B,IAAA,IAClC,CAEe+F,SAAAA,EAAQ9H,EAAO0H,EAAQC,GAItC,OAHAI,EAAAA,QAAc/H,EAAO0H,GACE,mBAAZC,GAAwBA,IAE5B3H,EAAQA,EAAmB+B,IAAA,IAClC,CAtDDrC,EAAAA,UAAUF,UAAUC,iBAAmB,CAAvC,EASA,CACC,qBACA,4BACA,uBACCwB,QAAQ,SAAA+G,GACTC,OAAOC,eAAexI,EAAAA,UAAUF,UAAWwI,EAAK,CAC/CG,cAAc,EACdtB,IAAM,WACL,OAAOpI,KAAK,UAAYuJ,EACxB,EACDd,IAL+C,SAK3CkB,GACHH,OAAOC,eAAezJ,KAAMuJ,EAAK,CAChCG,cAAc,EACdE,UAAU,EACVC,MAAOF,GAER,GAEF,GA6BD,IAAIG,EAAezI,EAAOA,QAAC0I,MAS3B,SAASC,IAET,CAAA,SAASC,IACR,OAAOjK,KAAKkK,YACZ,CAED,SAASC,IACR,OAAOnK,KAAKoK,gBACZ,CAhBD/I,UAAQ0I,MAAQ,SAAA7E,GAKf,OAJI4E,IAAc5E,EAAI4E,EAAa5E,IACnCA,EAAEmF,QAAUL,EACZ9E,EAAE+E,qBAAuBA,EACzB/E,EAAEiF,mBAAqBA,EACfjF,EAAEoF,YAAcpF,CACxB,EAYD,IAmHIqF,EAnHAC,EAAsB,CACzBd,cAAc,EACdtB,IAFyB,WAGxB,OAAYqC,KAAAA,KACZ,GAGEC,EAAerJ,EAAAA,QAAQE,MAC3BF,EAAOA,QAACE,MAAQ,SAAAA,GACf,IAAIC,EAAOD,EAAMC,KACblC,EAAQiC,EAAMjC,MACdqL,EAAkBrL,EAGtB,GAAoB,iBAATkC,EAAmB,CAC7B,IAAMoJ,GAA0C,IAAvBpJ,EAAKuG,QAAQ,KAGtC,IAAK,IAAIxI,KAFToL,EAAkB,CAAlB,EAEcrL,EAAO,CACpB,IAAIuK,EAAQvK,EAAMC,GAEduJ,GAAgB,aAANvJ,GAA6B,aAATiC,GAGjB,UAANjC,GAAiB,iBAAkBD,GAAkB,MAATuK,IAKhD,iBAANtK,GACA,UAAWD,GACI,MAAfA,EAAMuK,MAINtK,EAAI,QACY,aAANA,IAA8B,IAAVsK,EAM9BA,EAAQ,GACE,iBAAiBb,KAAKzJ,GAChCA,EAAI,aAEJ,6BAA6ByJ,KAAKzJ,EAAIiC,KACrCuH,EAAkBzJ,EAAMkC,MAEzBjC,EAAI,UACM,aAAayJ,KAAKzJ,GAC5BA,EAAI,YACM,YAAYyJ,KAAKzJ,GAC3BA,EAAI,aACM,mCAAmCyJ,KAAKzJ,GAClDA,EAAIA,EAAEsL,cACID,GAAoB/B,EAAYG,KAAKzJ,GAC/CA,EAAIA,EAAEuL,QAAQ,YAAa,OAAOD,cACd,OAAVhB,IACVA,OAAQkB,GAKL,aAAa/B,KAAKzJ,KACrBA,EAAIA,EAAEsL,cACFF,EAAgBpL,KACnBA,EAAI,mBAINoL,EAAgBpL,GAAKsK,EACrB,CAIQ,UAARrI,GACAmJ,EAAgBK,UAChBC,MAAMC,QAAQP,EAAgBd,SAG9Bc,EAAgBd,MAAQxH,EAAYA,aAAC/C,EAAM8C,UAAUI,QAAQ,SAAAwB,GAC5DA,EAAM1E,MAAM6L,UAC0C,GAArDR,EAAgBd,MAAM9B,QAAQ/D,EAAM1E,MAAMuK,MAC3C,IAIU,UAARrI,GAAoD,MAAhCmJ,EAAgBS,eACvCT,EAAgBd,MAAQxH,EAAYA,aAAC/C,EAAM8C,UAAUI,QAAQ,SAAAwB,GAE3DA,EAAM1E,MAAM6L,SADTR,EAAgBK,UAE0C,GAA5DL,EAAgBS,aAAarD,QAAQ/D,EAAM1E,MAAMuK,OAGjDc,EAAgBS,cAAgBpH,EAAM1E,MAAMuK,KAE9C,IAGFtI,EAAMjC,MAAQqL,EAEVrL,EAAMmL,OAASnL,EAAM+L,YACxBb,EAAoBc,WAAa,cAAehM,EACzB,MAAnBA,EAAM+L,YAAmBV,EAAgBF,MAAQnL,EAAM+L,WAC3D7B,OAAOC,eAAekB,EAAiB,YAAaH,GAErD,CAEDjJ,EAAMU,SAAW2G,EAEb8B,GAAcA,EAAanJ,EAC/B,EAID,IAAMgK,EAAkBlK,EAAHA,QAAAmK,IACrBnK,EAAOA,QAAPmK,IAAkB,SAASjK,GACtBgK,GACHA,EAAgBhK,GAEjBgJ,EAAmBhJ,EACnB+B,GAAA,EAMYmI,IAAAA,EAAqD,CACjEC,uBAAwB,CACvBjL,QAAS,CACRkL,YAAYzE,SAAAA,GACX,OAAOqD,EAAAqB,IAAgC1E,EAAhC5D,KAA6ChE,MAAMuK,KAC1D,KCrMEgC,EAAU,SAMhB,SAASC,EAActK,GACtB,OAAOZ,EAAaA,cAACmL,KAAK,KAAMvK,EAChC,CAOD,SAASwK,EAAeC,GACvB,QAASA,GAAWA,EAAQhK,WAAa2G,CACzC,CASD,SAASsD,EAAaD,GACrB,OAAKD,EAAeC,GACbE,EAAAA,aAAmBC,MAAM,KAAMC,WADDJ,CAErC,CAOD,SAASK,EAAuBjF,GAC/B,QAAIA,EAAJ9D,MACC6F,EAAYlH,OAAC,KAAMmF,IAEnB,EAED,CAOD,SAASkF,EAAYlJ,GACpB,OACEA,IACCA,EAAUmJ,MAAgC,IAAvBnJ,EAAUmE,UAAkBnE,IACjD,IAED,CAUKoJ,IAAAA,EAA0B,SAACvD,EAAUwD,GAAQxD,OAAAA,EAASwD,EAA5B,EAW1BC,EAAY,SAACzD,EAAUwD,UAAQxD,EAASwD,EAA5B,EAMZE,EAAalG,EAAAA,kBAEHmG,EAAgBC,GAC/BA,GACA,UAEeC,EAAiBC,GAChC,OAAOA,CACP,CAEeC,SAAAA,IACf,MAAO,EAAC,EAAOJ,EACf,CAIYK,IAAAA,EAAqBC,2BAMlBC,EAAqBC,EAAWC,GAC/C,IAAMzD,EAAQyD,IAEdC,EAAqCC,EAAAA,SAAS,CAC7CC,EAAW,CAAE/I,GAAQmF,EAAO6D,EAAcJ,KADlCG,EAAAA,EAAAA,GAAAA,EAAatH,OAyBtB,OArBAgH,EAAeA,gBAAC,WACfM,EAAA/I,GAAmBmF,EACnB4D,EAAUC,EAAeJ,EAEpB3N,EAAG8N,KAAkBH,MACzBnH,EAAY,CAAEsH,EAAAA,GAEf,EAAE,CAACJ,EAAWxD,EAAOyD,IAEtBK,EAASA,UAAC,WAKT,OAJKhO,EAAG8N,EAAkBA,GAAAA,EAAUC,MACnCvH,EAAY,CAAEsH,EAAAA,IAGRJ,EAAU,WACX1N,EAAG8N,EAAD/I,GAAmB+I,EAAUC,MACnCvH,EAAY,CAAEsH,EAAAA,GAEf,EACD,EAAE,CAACJ,IAEGxD,CACP,CAiCD,IAAe+D,GAAA,CACdJ,SAAAA,EADcA,SAEdK,MAAAA,EAFcA,MAGdC,WAAAA,EAHcA,WAIdH,UAAAA,EAJcA,UAKdR,gBAAAA,EALcA,gBAMdD,mBAAAA,EACAD,cAAAA,EACAF,iBAAAA,EACAK,qBAAAA,EACAP,gBAAAA,EACAkB,OAAAA,EAAAA,OACAC,oBAAAA,EAAAA,oBACAC,QAAAA,EAAAA,QACAC,YAAAA,EAAAA,YACAC,WAAAA,EAAAA,WACAC,cAAAA,EAAAA,cACAvC,QAAAA,EACAtJ,SAAAA,EACAL,OAAAA,EACAmH,QAAAA,EACAiD,uBAAAA,EACAtE,aAAAA,EACApH,cAAAA,EAAAA,cACAyN,cAAAA,EAAAA,cACAvC,cAAAA,EACAI,aAAAA,EACAoC,UAAAA,EA3BcA,UA4Bd5H,SAAAA,EA5BcA,SA6BdsF,eAAAA,EACAO,YAAAA,EACAtL,UAAAA,EA/BcA,UAgCdnB,cAAAA,EACAG,KAAAA,EACA4B,WAAAA,EACA8K,UAAAA,EACAF,wBAAAA,EACAG,WAAAA,EACAtI,SAAAA,EACAa,aAAAA,EACAP,KAAAA,EACA6G,mDAAAA"}