var r=require("preact"),e=0;function _(_,n,o,t,u,l){var f,i,c={};for(i in n)"ref"==i?f=n[i]:c[i]=n[i];var p={type:_,props:c,key:o,ref:f,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--e,__source:u,__self:l};if("function"==typeof _&&(f=_.defaultProps))for(i in f)void 0===c[i]&&(c[i]=f[i]);return r.options.vnode&&r.options.vnode(p),p}Object.defineProperty(exports,"Fragment",{enumerable:!0,get:function(){return r.Fragment}}),exports.jsx=_,exports.jsxDEV=_,exports.jsxs=_;
//# sourceMappingURL=jsxRuntime.js.map
