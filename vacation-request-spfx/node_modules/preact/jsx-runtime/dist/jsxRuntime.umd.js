!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("preact")):"function"==typeof define&&define.amd?define(["exports","preact"],n):n((e||self).jsxRuntime={},e.preact)}(this,function(e,n){var o=0;function t(e,t,r,f,i,u){var _,l,c={};for(l in t)"ref"==l?_=t[l]:c[l]=t[l];var p={type:e,props:c,key:r,ref:_,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--o,__source:i,__self:u};if("function"==typeof e&&(_=e.defaultProps))for(l in _)void 0===c[l]&&(c[l]=_[l]);return n.options.vnode&&n.options.vnode(p),p}Object.defineProperty(e,"Fragment",{enumerable:!0,get:function(){return n.Fragment}}),e.jsx=t,e.jsxDEV=t,e.jsxs=t});
//# sourceMappingURL=jsxRuntime.umd.js.map
