import{options as r}from"preact";export{Fragment}from"preact";var _=0;function o(o,e,n,t,f,l){var s,u,a={};for(u in e)"ref"==u?s=e[u]:a[u]=e[u];var i={type:o,props:a,key:n,ref:s,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--_,__source:f,__self:l};if("function"==typeof o&&(s=o.defaultProps))for(u in s)void 0===a[u]&&(a[u]=s[u]);return r.vnode&&r.vnode(i),i}export{o as jsx,o as jsxDEV,o as jsxs};
//# sourceMappingURL=jsxRuntime.module.js.map
