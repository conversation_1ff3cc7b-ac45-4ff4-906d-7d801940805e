var n,l,u,t,i,r,o,f,e,c={},s=[],a=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function h(n,l){for(var u in l)n[u]=l[u];return n}function p(n){var l=n.parentNode;l&&l.removeChild(n)}function v(l,u,t){var i,r,o,f={};for(o in u)"key"==o?i=u[o]:"ref"==o?r=u[o]:f[o]=u[o];if(arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):t),"function"==typeof l&&null!=l.defaultProps)for(o in l.defaultProps)void 0===f[o]&&(f[o]=l.defaultProps[o]);return y(l,f,i,r,null)}function y(n,t,i,r,o){var f={type:n,props:t,key:i,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++u:o};return null==o&&null!=l.vnode&&l.vnode(f),f}function d(n){return n.children}function _(n,l,u,t,i){var r;for(r in u)"children"===r||"key"===r||r in l||x(n,r,null,u[r],t);for(r in l)i&&"function"!=typeof l[r]||"children"===r||"key"===r||"value"===r||"checked"===r||u[r]===l[r]||x(n,r,l[r],u[r],t)}function k(n,l,u){"-"===l[0]?n.setProperty(l,null==u?"":u):n[l]=null==u?"":"number"!=typeof u||a.test(l)?u:u+"px"}function x(n,l,u,t,i){var r;n:if("style"===l)if("string"==typeof u)n.style.cssText=u;else{if("string"==typeof t&&(n.style.cssText=t=""),t)for(l in t)u&&l in u||k(n.style,l,"");if(u)for(l in u)t&&u[l]===t[l]||k(n.style,l,u[l])}else if("o"===l[0]&&"n"===l[1])r=l!==(l=l.replace(/Capture$/,"")),l=l.toLowerCase()in n?l.toLowerCase().slice(2):l.slice(2),n.l||(n.l={}),n.l[l+r]=u,u?t||n.addEventListener(l,r?g:b,r):n.removeEventListener(l,r?g:b,r);else if("dangerouslySetInnerHTML"!==l){if(i)l=l.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==l&&"height"!==l&&"href"!==l&&"list"!==l&&"form"!==l&&"tabIndex"!==l&&"download"!==l&&l in n)try{n[l]=null==u?"":u;break n}catch(n){}"function"==typeof u||(null==u||!1===u&&-1==l.indexOf("-")?n.removeAttribute(l):n.setAttribute(l,u))}}function b(n){i=!0;try{return this.l[n.type+!1](l.event?l.event(n):n)}finally{i=!1}}function g(n){i=!0;try{return this.l[n.type+!0](l.event?l.event(n):n)}finally{i=!1}}function m(n,l){this.props=n,this.context=l}function w(n,l){if(null==l)return n.__?w(n.__,n.__.__k.indexOf(n)+1):null;for(var u;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e)return u.__e;return"function"==typeof n.type?w(n):null}function A(n){var l,u;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e){n.__e=n.__c.base=u.__e;break}return A(n)}}function P(n){i?setTimeout(n):f(n)}function C(n){(!n.__d&&(n.__d=!0)&&r.push(n)&&!T.__r++||o!==l.debounceRendering)&&((o=l.debounceRendering)||P)(T)}function T(){var n,l,u,t,i,o,f,e;for(r.sort(function(n,l){return n.__v.__b-l.__v.__b});n=r.shift();)n.__d&&(l=r.length,t=void 0,i=void 0,f=(o=(u=n).__v).__e,(e=u.__P)&&(t=[],(i=h({},o)).__v=o.__v+1,z(e,o,i,u.__n,void 0!==e.ownerSVGElement,null!=o.__h?[f]:null,t,null==f?w(o):f,o.__h),L(t,o),o.__e!=f&&A(o)),r.length>l&&r.sort(function(n,l){return n.__v.__b-l.__v.__b}));T.__r=0}function $(n,l,u,t,i,r,o,f,e,a){var h,p,v,_,k,x,b,g=t&&t.__k||s,m=g.length;for(u.__k=[],h=0;h<l.length;h++)if(null!=(_=u.__k[h]=null==(_=l[h])||"boolean"==typeof _?null:"string"==typeof _||"number"==typeof _||"bigint"==typeof _?y(null,_,null,null,_):Array.isArray(_)?y(d,{children:_},null,null,null):_.__b>0?y(_.type,_.props,_.key,_.ref?_.ref:null,_.__v):_)){if(_.__=u,_.__b=u.__b+1,null===(v=g[h])||v&&_.key==v.key&&_.type===v.type)g[h]=void 0;else for(p=0;p<m;p++){if((v=g[p])&&_.key==v.key&&_.type===v.type){g[p]=void 0;break}v=null}z(n,_,v=v||c,i,r,o,f,e,a),k=_.__e,(p=_.ref)&&v.ref!=p&&(b||(b=[]),v.ref&&b.push(v.ref,null,_),b.push(p,_.__c||k,_)),null!=k?(null==x&&(x=k),"function"==typeof _.type&&_.__k===v.__k?_.__d=e=H(_,e,n):e=I(n,_,v,g,k,e),"function"==typeof u.type&&(u.__d=e)):e&&v.__e==e&&e.parentNode!=n&&(e=w(v))}for(u.__e=x,h=m;h--;)null!=g[h]&&("function"==typeof u.type&&null!=g[h].__e&&g[h].__e==u.__d&&(u.__d=j(t).nextSibling),O(g[h],g[h]));if(b)for(h=0;h<b.length;h++)N(b[h],b[++h],b[++h])}function H(n,l,u){for(var t,i=n.__k,r=0;i&&r<i.length;r++)(t=i[r])&&(t.__=n,l="function"==typeof t.type?H(t,l,u):I(u,t,t,i,t.__e,l));return l}function I(n,l,u,t,i,r){var o,f,e;if(void 0!==l.__d)o=l.__d,l.__d=void 0;else if(null==u||i!=r||null==i.parentNode)n:if(null==r||r.parentNode!==n)n.appendChild(i),o=null;else{for(f=r,e=0;(f=f.nextSibling)&&e<t.length;e+=1)if(f==i)break n;n.insertBefore(i,r),o=r}return void 0!==o?o:i.nextSibling}function j(n){var l,u,t;if(null==n.type||"string"==typeof n.type)return n.__e;if(n.__k)for(l=n.__k.length-1;l>=0;l--)if((u=n.__k[l])&&(t=j(u)))return t;return null}function z(n,u,t,i,r,o,f,e,c){var s,a,p,v,y,_,k,x,b,g,w,A,P,C,T,H=u.type;if(void 0!==u.constructor)return null;null!=t.__h&&(c=t.__h,e=u.__e=t.__e,u.__h=null,o=[e]),(s=l.__b)&&s(u);try{n:if("function"==typeof H){if(x=u.props,b=(s=H.contextType)&&i[s.__c],g=s?b?b.props.value:s.__:i,t.__c?k=(a=u.__c=t.__c).__=a.__E:("prototype"in H&&H.prototype.render?u.__c=a=new H(x,g):(u.__c=a=new m(x,g),a.constructor=H,a.render=S),b&&b.sub(a),a.props=x,a.state||(a.state={}),a.context=g,a.__n=i,p=a.__d=!0,a.__h=[],a._sb=[]),null==a.__s&&(a.__s=a.state),null!=H.getDerivedStateFromProps&&(a.__s==a.state&&(a.__s=h({},a.__s)),h(a.__s,H.getDerivedStateFromProps(x,a.__s))),v=a.props,y=a.state,a.__v=u,p)null==H.getDerivedStateFromProps&&null!=a.componentWillMount&&a.componentWillMount(),null!=a.componentDidMount&&a.__h.push(a.componentDidMount);else{if(null==H.getDerivedStateFromProps&&x!==v&&null!=a.componentWillReceiveProps&&a.componentWillReceiveProps(x,g),!a.__e&&null!=a.shouldComponentUpdate&&!1===a.shouldComponentUpdate(x,a.__s,g)||u.__v===t.__v){for(u.__v!==t.__v&&(a.props=x,a.state=a.__s,a.__d=!1),u.__e=t.__e,u.__k=t.__k,u.__k.forEach(function(n){n&&(n.__=u)}),w=0;w<a._sb.length;w++)a.__h.push(a._sb[w]);a._sb=[],a.__h.length&&f.push(a);break n}null!=a.componentWillUpdate&&a.componentWillUpdate(x,a.__s,g),null!=a.componentDidUpdate&&a.__h.push(function(){a.componentDidUpdate(v,y,_)})}if(a.context=g,a.props=x,a.__P=n,A=l.__r,P=0,"prototype"in H&&H.prototype.render){for(a.state=a.__s,a.__d=!1,A&&A(u),s=a.render(a.props,a.state,a.context),C=0;C<a._sb.length;C++)a.__h.push(a._sb[C]);a._sb=[]}else do{a.__d=!1,A&&A(u),s=a.render(a.props,a.state,a.context),a.state=a.__s}while(a.__d&&++P<25);a.state=a.__s,null!=a.getChildContext&&(i=h(h({},i),a.getChildContext())),p||null==a.getSnapshotBeforeUpdate||(_=a.getSnapshotBeforeUpdate(v,y)),T=null!=s&&s.type===d&&null==s.key?s.props.children:s,$(n,Array.isArray(T)?T:[T],u,t,i,r,o,f,e,c),a.base=u.__e,u.__h=null,a.__h.length&&f.push(a),k&&(a.__E=a.__=null),a.__e=!1}else null==o&&u.__v===t.__v?(u.__k=t.__k,u.__e=t.__e):u.__e=M(t.__e,u,t,i,r,o,f,c);(s=l.diffed)&&s(u)}catch(n){u.__v=null,(c||null!=o)&&(u.__e=e,u.__h=!!c,o[o.indexOf(e)]=null),l.__e(n,u,t)}}function L(n,u){l.__c&&l.__c(u,n),n.some(function(u){try{n=u.__h,u.__h=[],n.some(function(n){n.call(u)})}catch(n){l.__e(n,u.__v)}})}function M(l,u,t,i,r,o,f,e){var s,a,h,v=t.props,y=u.props,d=u.type,k=0;if("svg"===d&&(r=!0),null!=o)for(;k<o.length;k++)if((s=o[k])&&"setAttribute"in s==!!d&&(d?s.localName===d:3===s.nodeType)){l=s,o[k]=null;break}if(null==l){if(null===d)return document.createTextNode(y);l=r?document.createElementNS("http://www.w3.org/2000/svg",d):document.createElement(d,y.is&&y),o=null,e=!1}if(null===d)v===y||e&&l.data===y||(l.data=y);else{if(o=o&&n.call(l.childNodes),a=(v=t.props||c).dangerouslySetInnerHTML,h=y.dangerouslySetInnerHTML,!e){if(null!=o)for(v={},k=0;k<l.attributes.length;k++)v[l.attributes[k].name]=l.attributes[k].value;(h||a)&&(h&&(a&&h.__html==a.__html||h.__html===l.innerHTML)||(l.innerHTML=h&&h.__html||""))}if(_(l,y,v,r,e),h)u.__k=[];else if(k=u.props.children,$(l,Array.isArray(k)?k:[k],u,t,i,r&&"foreignObject"!==d,o,f,o?o[0]:t.__k&&w(t,0),e),null!=o)for(k=o.length;k--;)null!=o[k]&&p(o[k]);e||("value"in y&&void 0!==(k=y.value)&&(k!==l.value||"progress"===d&&!k||"option"===d&&k!==v.value)&&x(l,"value",k,v.value,!1),"checked"in y&&void 0!==(k=y.checked)&&k!==l.checked&&x(l,"checked",k,v.checked,!1))}return l}function N(n,u,t){try{"function"==typeof n?n(u):n.current=u}catch(n){l.__e(n,t)}}function O(n,u,t){var i,r;if(l.unmount&&l.unmount(n),(i=n.ref)&&(i.current&&i.current!==n.__e||N(i,null,u)),null!=(i=n.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(n){l.__e(n,u)}i.base=i.__P=null,n.__c=void 0}if(i=n.__k)for(r=0;r<i.length;r++)i[r]&&O(i[r],u,t||"function"!=typeof n.type);t||null==n.__e||p(n.__e),n.__=n.__e=n.__d=void 0}function S(n,l,u){return this.constructor(n,u)}function q(u,t,i){var r,o,f;l.__&&l.__(u,t),o=(r="function"==typeof i)?null:i&&i.__k||t.__k,f=[],z(t,u=(!r&&i||t).__k=v(d,null,[u]),o||c,c,void 0!==t.ownerSVGElement,!r&&i?[i]:o?null:t.firstChild?n.call(t.childNodes):null,f,!r&&i?i:o?o.__e:t.firstChild,r),L(f,u)}n=s.slice,l={__e:function(n,l,u,t){for(var i,r,o;l=l.__;)if((i=l.__c)&&!i.__)try{if((r=i.constructor)&&null!=r.getDerivedStateFromError&&(i.setState(r.getDerivedStateFromError(n)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(n,t||{}),o=i.__d),o)return i.__E=i}catch(l){n=l}throw n}},u=0,t=function(n){return null!=n&&void 0===n.constructor},i=!1,m.prototype.setState=function(n,l){var u;u=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=h({},this.state),"function"==typeof n&&(n=n(h({},u),this.props)),n&&h(u,n),null!=n&&this.__v&&(l&&this._sb.push(l),C(this))},m.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),C(this))},m.prototype.render=d,r=[],f="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,T.__r=0,e=0,exports.Component=m,exports.Fragment=d,exports.cloneElement=function(l,u,t){var i,r,o,f=h({},l.props);for(o in u)"key"==o?i=u[o]:"ref"==o?r=u[o]:f[o]=u[o];return arguments.length>2&&(f.children=arguments.length>3?n.call(arguments,2):t),y(l.type,f,i||l.key,r||l.ref,null)},exports.createContext=function(n,l){var u={__c:l="__cC"+e++,__:n,Consumer:function(n,l){return n.children(l)},Provider:function(n){var u,t;return this.getChildContext||(u=[],(t={})[l]=this,this.getChildContext=function(){return t},this.shouldComponentUpdate=function(n){this.props.value!==n.value&&u.some(function(n){n.__e=!0,C(n)})},this.sub=function(n){u.push(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){u.splice(u.indexOf(n),1),l&&l.call(n)}}),n.children}};return u.Provider.__=u.Consumer.contextType=u},exports.createElement=v,exports.createRef=function(){return{current:null}},exports.h=v,exports.hydrate=function n(l,u){q(l,u,n)},exports.isValidElement=t,exports.options=l,exports.render=q,exports.toChildArray=function n(l,u){return u=u||[],null==l||"boolean"==typeof l||(Array.isArray(l)?l.some(function(l){n(l,u)}):u.push(l)),u};
//# sourceMappingURL=preact.js.map
