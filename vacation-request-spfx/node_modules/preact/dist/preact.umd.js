!function(n,l){"object"==typeof exports&&"undefined"!=typeof module?l(exports):"function"==typeof define&&define.amd?define(["exports"],l):l((n||self).preact={})}(this,function(n){var l,u,i,t,o,f,r,e,c,s={},a=[],h=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function y(n,l){for(var u in l)n[u]=l[u];return n}function v(n){var l=n.parentNode;l&&l.removeChild(n)}function d(n,u,i){var t,o,f,r={};for(f in u)"key"==f?t=u[f]:"ref"==f?o=u[f]:r[f]=u[f];if(arguments.length>2&&(r.children=arguments.length>3?l.call(arguments,2):i),"function"==typeof n&&null!=n.defaultProps)for(f in n.defaultProps)void 0===r[f]&&(r[f]=n.defaultProps[f]);return p(n,r,t,o,null)}function p(n,l,t,o,f){var r={type:n,props:l,key:t,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==f?++i:f};return null==f&&null!=u.vnode&&u.vnode(r),r}function _(n){return n.children}function b(n,l,u,i,t){var o;for(o in u)"children"===o||"key"===o||o in l||k(n,o,null,u[o],i);for(o in l)t&&"function"!=typeof l[o]||"children"===o||"key"===o||"value"===o||"checked"===o||u[o]===l[o]||k(n,o,l[o],u[o],i)}function g(n,l,u){"-"===l[0]?n.setProperty(l,null==u?"":u):n[l]=null==u?"":"number"!=typeof u||h.test(l)?u:u+"px"}function k(n,l,u,i,t){var o;n:if("style"===l)if("string"==typeof u)n.style.cssText=u;else{if("string"==typeof i&&(n.style.cssText=i=""),i)for(l in i)u&&l in u||g(n.style,l,"");if(u)for(l in u)i&&u[l]===i[l]||g(n.style,l,u[l])}else if("o"===l[0]&&"n"===l[1])o=l!==(l=l.replace(/Capture$/,"")),l=l.toLowerCase()in n?l.toLowerCase().slice(2):l.slice(2),n.l||(n.l={}),n.l[l+o]=u,u?i||n.addEventListener(l,o?w:m,o):n.removeEventListener(l,o?w:m,o);else if("dangerouslySetInnerHTML"!==l){if(t)l=l.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==l&&"height"!==l&&"href"!==l&&"list"!==l&&"form"!==l&&"tabIndex"!==l&&"download"!==l&&l in n)try{n[l]=null==u?"":u;break n}catch(n){}"function"==typeof u||(null==u||!1===u&&-1==l.indexOf("-")?n.removeAttribute(l):n.setAttribute(l,u))}}function m(n){o=!0;try{return this.l[n.type+!1](u.event?u.event(n):n)}finally{o=!1}}function w(n){o=!0;try{return this.l[n.type+!0](u.event?u.event(n):n)}finally{o=!1}}function x(n,l){this.props=n,this.context=l}function A(n,l){if(null==l)return n.__?A(n.__,n.__.__k.indexOf(n)+1):null;for(var u;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e)return u.__e;return"function"==typeof n.type?A(n):null}function T(n){var l,u;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e){n.__e=n.__c.base=u.__e;break}return T(n)}}function P(n){o?setTimeout(n):e(n)}function C(n){(!n.__d&&(n.__d=!0)&&f.push(n)&&!$.__r++||r!==u.debounceRendering)&&((r=u.debounceRendering)||P)($)}function $(){var n,l,u,i,t,o,r,e;for(f.sort(function(n,l){return n.__v.__b-l.__v.__b});n=f.shift();)n.__d&&(l=f.length,i=void 0,t=void 0,r=(o=(u=n).__v).__e,(e=u.__P)&&(i=[],(t=y({},o)).__v=o.__v+1,L(e,o,t,u.__n,void 0!==e.ownerSVGElement,null!=o.__h?[r]:null,i,null==r?A(o):r,o.__h),M(i,o),o.__e!=r&&T(o)),f.length>l&&f.sort(function(n,l){return n.__v.__b-l.__v.__b}));$.__r=0}function j(n,l,u,i,t,o,f,r,e,c){var h,y,v,d,b,g,k,m=i&&i.__k||a,w=m.length;for(u.__k=[],h=0;h<l.length;h++)if(null!=(d=u.__k[h]=null==(d=l[h])||"boolean"==typeof d?null:"string"==typeof d||"number"==typeof d||"bigint"==typeof d?p(null,d,null,null,d):Array.isArray(d)?p(_,{children:d},null,null,null):d.__b>0?p(d.type,d.props,d.key,d.ref?d.ref:null,d.__v):d)){if(d.__=u,d.__b=u.__b+1,null===(v=m[h])||v&&d.key==v.key&&d.type===v.type)m[h]=void 0;else for(y=0;y<w;y++){if((v=m[y])&&d.key==v.key&&d.type===v.type){m[y]=void 0;break}v=null}L(n,d,v=v||s,t,o,f,r,e,c),b=d.__e,(y=d.ref)&&v.ref!=y&&(k||(k=[]),v.ref&&k.push(v.ref,null,d),k.push(y,d.__c||b,d)),null!=b?(null==g&&(g=b),"function"==typeof d.type&&d.__k===v.__k?d.__d=e=H(d,e,n):e=I(n,d,v,m,b,e),"function"==typeof u.type&&(u.__d=e)):e&&v.__e==e&&e.parentNode!=n&&(e=A(v))}for(u.__e=g,h=w;h--;)null!=m[h]&&("function"==typeof u.type&&null!=m[h].__e&&m[h].__e==u.__d&&(u.__d=z(i).nextSibling),S(m[h],m[h]));if(k)for(h=0;h<k.length;h++)O(k[h],k[++h],k[++h])}function H(n,l,u){for(var i,t=n.__k,o=0;t&&o<t.length;o++)(i=t[o])&&(i.__=n,l="function"==typeof i.type?H(i,l,u):I(u,i,i,t,i.__e,l));return l}function I(n,l,u,i,t,o){var f,r,e;if(void 0!==l.__d)f=l.__d,l.__d=void 0;else if(null==u||t!=o||null==t.parentNode)n:if(null==o||o.parentNode!==n)n.appendChild(t),f=null;else{for(r=o,e=0;(r=r.nextSibling)&&e<i.length;e+=1)if(r==t)break n;n.insertBefore(t,o),f=o}return void 0!==f?f:t.nextSibling}function z(n){var l,u,i;if(null==n.type||"string"==typeof n.type)return n.__e;if(n.__k)for(l=n.__k.length-1;l>=0;l--)if((u=n.__k[l])&&(i=z(u)))return i;return null}function L(n,l,i,t,o,f,r,e,c){var s,a,h,v,d,p,b,g,k,m,w,A,T,P,C,$=l.type;if(void 0!==l.constructor)return null;null!=i.__h&&(c=i.__h,e=l.__e=i.__e,l.__h=null,f=[e]),(s=u.__b)&&s(l);try{n:if("function"==typeof $){if(g=l.props,k=(s=$.contextType)&&t[s.__c],m=s?k?k.props.value:s.__:t,i.__c?b=(a=l.__c=i.__c).__=a.__E:("prototype"in $&&$.prototype.render?l.__c=a=new $(g,m):(l.__c=a=new x(g,m),a.constructor=$,a.render=q),k&&k.sub(a),a.props=g,a.state||(a.state={}),a.context=m,a.__n=t,h=a.__d=!0,a.__h=[],a._sb=[]),null==a.__s&&(a.__s=a.state),null!=$.getDerivedStateFromProps&&(a.__s==a.state&&(a.__s=y({},a.__s)),y(a.__s,$.getDerivedStateFromProps(g,a.__s))),v=a.props,d=a.state,a.__v=l,h)null==$.getDerivedStateFromProps&&null!=a.componentWillMount&&a.componentWillMount(),null!=a.componentDidMount&&a.__h.push(a.componentDidMount);else{if(null==$.getDerivedStateFromProps&&g!==v&&null!=a.componentWillReceiveProps&&a.componentWillReceiveProps(g,m),!a.__e&&null!=a.shouldComponentUpdate&&!1===a.shouldComponentUpdate(g,a.__s,m)||l.__v===i.__v){for(l.__v!==i.__v&&(a.props=g,a.state=a.__s,a.__d=!1),l.__e=i.__e,l.__k=i.__k,l.__k.forEach(function(n){n&&(n.__=l)}),w=0;w<a._sb.length;w++)a.__h.push(a._sb[w]);a._sb=[],a.__h.length&&r.push(a);break n}null!=a.componentWillUpdate&&a.componentWillUpdate(g,a.__s,m),null!=a.componentDidUpdate&&a.__h.push(function(){a.componentDidUpdate(v,d,p)})}if(a.context=m,a.props=g,a.__P=n,A=u.__r,T=0,"prototype"in $&&$.prototype.render){for(a.state=a.__s,a.__d=!1,A&&A(l),s=a.render(a.props,a.state,a.context),P=0;P<a._sb.length;P++)a.__h.push(a._sb[P]);a._sb=[]}else do{a.__d=!1,A&&A(l),s=a.render(a.props,a.state,a.context),a.state=a.__s}while(a.__d&&++T<25);a.state=a.__s,null!=a.getChildContext&&(t=y(y({},t),a.getChildContext())),h||null==a.getSnapshotBeforeUpdate||(p=a.getSnapshotBeforeUpdate(v,d)),C=null!=s&&s.type===_&&null==s.key?s.props.children:s,j(n,Array.isArray(C)?C:[C],l,i,t,o,f,r,e,c),a.base=l.__e,l.__h=null,a.__h.length&&r.push(a),b&&(a.__E=a.__=null),a.__e=!1}else null==f&&l.__v===i.__v?(l.__k=i.__k,l.__e=i.__e):l.__e=N(i.__e,l,i,t,o,f,r,c);(s=u.diffed)&&s(l)}catch(n){l.__v=null,(c||null!=f)&&(l.__e=e,l.__h=!!c,f[f.indexOf(e)]=null),u.__e(n,l,i)}}function M(n,l){u.__c&&u.__c(l,n),n.some(function(l){try{n=l.__h,l.__h=[],n.some(function(n){n.call(l)})}catch(n){u.__e(n,l.__v)}})}function N(n,u,i,t,o,f,r,e){var c,a,h,y=i.props,d=u.props,p=u.type,_=0;if("svg"===p&&(o=!0),null!=f)for(;_<f.length;_++)if((c=f[_])&&"setAttribute"in c==!!p&&(p?c.localName===p:3===c.nodeType)){n=c,f[_]=null;break}if(null==n){if(null===p)return document.createTextNode(d);n=o?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,d.is&&d),f=null,e=!1}if(null===p)y===d||e&&n.data===d||(n.data=d);else{if(f=f&&l.call(n.childNodes),a=(y=i.props||s).dangerouslySetInnerHTML,h=d.dangerouslySetInnerHTML,!e){if(null!=f)for(y={},_=0;_<n.attributes.length;_++)y[n.attributes[_].name]=n.attributes[_].value;(h||a)&&(h&&(a&&h.__html==a.__html||h.__html===n.innerHTML)||(n.innerHTML=h&&h.__html||""))}if(b(n,d,y,o,e),h)u.__k=[];else if(_=u.props.children,j(n,Array.isArray(_)?_:[_],u,i,t,o&&"foreignObject"!==p,f,r,f?f[0]:i.__k&&A(i,0),e),null!=f)for(_=f.length;_--;)null!=f[_]&&v(f[_]);e||("value"in d&&void 0!==(_=d.value)&&(_!==n.value||"progress"===p&&!_||"option"===p&&_!==y.value)&&k(n,"value",_,y.value,!1),"checked"in d&&void 0!==(_=d.checked)&&_!==n.checked&&k(n,"checked",_,y.checked,!1))}return n}function O(n,l,i){try{"function"==typeof n?n(l):n.current=l}catch(n){u.__e(n,i)}}function S(n,l,i){var t,o;if(u.unmount&&u.unmount(n),(t=n.ref)&&(t.current&&t.current!==n.__e||O(t,null,l)),null!=(t=n.__c)){if(t.componentWillUnmount)try{t.componentWillUnmount()}catch(n){u.__e(n,l)}t.base=t.__P=null,n.__c=void 0}if(t=n.__k)for(o=0;o<t.length;o++)t[o]&&S(t[o],l,i||"function"!=typeof n.type);i||null==n.__e||v(n.__e),n.__=n.__e=n.__d=void 0}function q(n,l,u){return this.constructor(n,u)}function B(n,i,t){var o,f,r;u.__&&u.__(n,i),f=(o="function"==typeof t)?null:t&&t.__k||i.__k,r=[],L(i,n=(!o&&t||i).__k=d(_,null,[n]),f||s,s,void 0!==i.ownerSVGElement,!o&&t?[t]:f?null:i.firstChild?l.call(i.childNodes):null,r,!o&&t?t:f?f.__e:i.firstChild,o),M(r,n)}l=a.slice,u={__e:function(n,l,u,i){for(var t,o,f;l=l.__;)if((t=l.__c)&&!t.__)try{if((o=t.constructor)&&null!=o.getDerivedStateFromError&&(t.setState(o.getDerivedStateFromError(n)),f=t.__d),null!=t.componentDidCatch&&(t.componentDidCatch(n,i||{}),f=t.__d),f)return t.__E=t}catch(l){n=l}throw n}},i=0,t=function(n){return null!=n&&void 0===n.constructor},o=!1,x.prototype.setState=function(n,l){var u;u=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=y({},this.state),"function"==typeof n&&(n=n(y({},u),this.props)),n&&y(u,n),null!=n&&this.__v&&(l&&this._sb.push(l),C(this))},x.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),C(this))},x.prototype.render=_,f=[],e="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,$.__r=0,c=0,n.Component=x,n.Fragment=_,n.cloneElement=function(n,u,i){var t,o,f,r=y({},n.props);for(f in u)"key"==f?t=u[f]:"ref"==f?o=u[f]:r[f]=u[f];return arguments.length>2&&(r.children=arguments.length>3?l.call(arguments,2):i),p(n.type,r,t||n.key,o||n.ref,null)},n.createContext=function(n,l){var u={__c:l="__cC"+c++,__:n,Consumer:function(n,l){return n.children(l)},Provider:function(n){var u,i;return this.getChildContext||(u=[],(i={})[l]=this,this.getChildContext=function(){return i},this.shouldComponentUpdate=function(n){this.props.value!==n.value&&u.some(function(n){n.__e=!0,C(n)})},this.sub=function(n){u.push(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){u.splice(u.indexOf(n),1),l&&l.call(n)}}),n.children}};return u.Provider.__=u.Consumer.contextType=u},n.createElement=d,n.createRef=function(){return{current:null}},n.h=d,n.hydrate=function n(l,u){B(l,u,n)},n.isValidElement=t,n.options=u,n.render=B,n.toChildArray=function n(l,u){return u=u||[],null==l||"boolean"==typeof l||(Array.isArray(l)?l.some(function(l){n(l,u)}):u.push(l)),u}});
//# sourceMappingURL=preact.umd.js.map
