var n,l,u,t,i,r,o,e,f,c,s={},a=[],h=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function y(n,l){for(var u in l)n[u]=l[u];return n}function v(n){var l=n.parentNode;l&&l.removeChild(n)}function d(l,u,t){var i,r,o,e={};for(o in u)"key"==o?i=u[o]:"ref"==o?r=u[o]:e[o]=u[o];if(arguments.length>2&&(e.children=arguments.length>3?n.call(arguments,2):t),"function"==typeof l&&null!=l.defaultProps)for(o in l.defaultProps)void 0===e[o]&&(e[o]=l.defaultProps[o]);return p(l,e,i,r,null)}function p(n,t,i,r,o){var e={type:n,props:t,key:i,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==o?++u:o};return null==o&&null!=l.vnode&&l.vnode(e),e}function _(n){return n.children}function m(n,l,u,t,i){var r;for(r in u)"children"===r||"key"===r||r in l||b(n,r,null,u[r],t);for(r in l)i&&"function"!=typeof l[r]||"children"===r||"key"===r||"value"===r||"checked"===r||u[r]===l[r]||b(n,r,l[r],u[r],t)}function k(n,l,u){"-"===l[0]?n.setProperty(l,null==u?"":u):n[l]=null==u?"":"number"!=typeof u||h.test(l)?u:u+"px"}function b(n,l,u,t,i){var r;n:if("style"===l)if("string"==typeof u)n.style.cssText=u;else{if("string"==typeof t&&(n.style.cssText=t=""),t)for(l in t)u&&l in u||k(n.style,l,"");if(u)for(l in u)t&&u[l]===t[l]||k(n.style,l,u[l])}else if("o"===l[0]&&"n"===l[1])r=l!==(l=l.replace(/Capture$/,"")),l=l.toLowerCase()in n?l.toLowerCase().slice(2):l.slice(2),n.l||(n.l={}),n.l[l+r]=u,u?t||n.addEventListener(l,r?w:g,r):n.removeEventListener(l,r?w:g,r);else if("dangerouslySetInnerHTML"!==l){if(i)l=l.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==l&&"height"!==l&&"href"!==l&&"list"!==l&&"form"!==l&&"tabIndex"!==l&&"download"!==l&&l in n)try{n[l]=null==u?"":u;break n}catch(n){}"function"==typeof u||(null==u||!1===u&&-1==l.indexOf("-")?n.removeAttribute(l):n.setAttribute(l,u))}}function g(n){i=!0;try{return this.l[n.type+!1](l.event?l.event(n):n)}finally{i=!1}}function w(n){i=!0;try{return this.l[n.type+!0](l.event?l.event(n):n)}finally{i=!1}}function A(n,l){this.props=n,this.context=l}function C(n,l){if(null==l)return n.__?C(n.__,n.__.__k.indexOf(n)+1):null;for(var u;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e)return u.__e;return"function"==typeof n.type?C(n):null}function x(n){var l,u;if(null!=(n=n.__)&&null!=n.__c){for(n.__e=n.__c.base=null,l=0;l<n.__k.length;l++)if(null!=(u=n.__k[l])&&null!=u.__e){n.__e=n.__c.base=u.__e;break}return x(n)}}function P(n){i?setTimeout(n):e(n)}function E(n){(!n.__d&&(n.__d=!0)&&r.push(n)&&!T.__r++||o!==l.debounceRendering)&&((o=l.debounceRendering)||P)(T)}function T(){var n,l,u,t,i,o,e,f;for(r.sort(function(n,l){return n.__v.__b-l.__v.__b});n=r.shift();)n.__d&&(l=r.length,t=void 0,i=void 0,e=(o=(u=n).__v).__e,(f=u.__P)&&(t=[],(i=y({},o)).__v=o.__v+1,z(f,o,i,u.__n,void 0!==f.ownerSVGElement,null!=o.__h?[e]:null,t,null==e?C(o):e,o.__h),F(t,o),o.__e!=e&&x(o)),r.length>l&&r.sort(function(n,l){return n.__v.__b-l.__v.__b}));T.__r=0}function $(n,l,u,t,i,r,o,e,f,c){var h,y,v,d,m,k,b,g=t&&t.__k||a,w=g.length;for(u.__k=[],h=0;h<l.length;h++)if(null!=(d=u.__k[h]=null==(d=l[h])||"boolean"==typeof d?null:"string"==typeof d||"number"==typeof d||"bigint"==typeof d?p(null,d,null,null,d):Array.isArray(d)?p(_,{children:d},null,null,null):d.__b>0?p(d.type,d.props,d.key,d.ref?d.ref:null,d.__v):d)){if(d.__=u,d.__b=u.__b+1,null===(v=g[h])||v&&d.key==v.key&&d.type===v.type)g[h]=void 0;else for(y=0;y<w;y++){if((v=g[y])&&d.key==v.key&&d.type===v.type){g[y]=void 0;break}v=null}z(n,d,v=v||s,i,r,o,e,f,c),m=d.__e,(y=d.ref)&&v.ref!=y&&(b||(b=[]),v.ref&&b.push(v.ref,null,d),b.push(y,d.__c||m,d)),null!=m?(null==k&&(k=m),"function"==typeof d.type&&d.__k===v.__k?d.__d=f=H(d,f,n):f=I(n,d,v,g,m,f),"function"==typeof u.type&&(u.__d=f)):f&&v.__e==f&&f.parentNode!=n&&(f=C(v))}for(u.__e=k,h=w;h--;)null!=g[h]&&("function"==typeof u.type&&null!=g[h].__e&&g[h].__e==u.__d&&(u.__d=j(t).nextSibling),N(g[h],g[h]));if(b)for(h=0;h<b.length;h++)M(b[h],b[++h],b[++h])}function H(n,l,u){for(var t,i=n.__k,r=0;i&&r<i.length;r++)(t=i[r])&&(t.__=n,l="function"==typeof t.type?H(t,l,u):I(u,t,t,i,t.__e,l));return l}function I(n,l,u,t,i,r){var o,e,f;if(void 0!==l.__d)o=l.__d,l.__d=void 0;else if(null==u||i!=r||null==i.parentNode)n:if(null==r||r.parentNode!==n)n.appendChild(i),o=null;else{for(e=r,f=0;(e=e.nextSibling)&&f<t.length;f+=1)if(e==i)break n;n.insertBefore(i,r),o=r}return void 0!==o?o:i.nextSibling}function j(n){var l,u,t;if(null==n.type||"string"==typeof n.type)return n.__e;if(n.__k)for(l=n.__k.length-1;l>=0;l--)if((u=n.__k[l])&&(t=j(u)))return t;return null}function z(n,u,t,i,r,o,e,f,c){var s,a,h,v,d,p,m,k,b,g,w,C,x,P,E,T=u.type;if(void 0!==u.constructor)return null;null!=t.__h&&(c=t.__h,f=u.__e=t.__e,u.__h=null,o=[f]),(s=l.__b)&&s(u);try{n:if("function"==typeof T){if(k=u.props,b=(s=T.contextType)&&i[s.__c],g=s?b?b.props.value:s.__:i,t.__c?m=(a=u.__c=t.__c).__=a.__E:("prototype"in T&&T.prototype.render?u.__c=a=new T(k,g):(u.__c=a=new A(k,g),a.constructor=T,a.render=O),b&&b.sub(a),a.props=k,a.state||(a.state={}),a.context=g,a.__n=i,h=a.__d=!0,a.__h=[],a._sb=[]),null==a.__s&&(a.__s=a.state),null!=T.getDerivedStateFromProps&&(a.__s==a.state&&(a.__s=y({},a.__s)),y(a.__s,T.getDerivedStateFromProps(k,a.__s))),v=a.props,d=a.state,a.__v=u,h)null==T.getDerivedStateFromProps&&null!=a.componentWillMount&&a.componentWillMount(),null!=a.componentDidMount&&a.__h.push(a.componentDidMount);else{if(null==T.getDerivedStateFromProps&&k!==v&&null!=a.componentWillReceiveProps&&a.componentWillReceiveProps(k,g),!a.__e&&null!=a.shouldComponentUpdate&&!1===a.shouldComponentUpdate(k,a.__s,g)||u.__v===t.__v){for(u.__v!==t.__v&&(a.props=k,a.state=a.__s,a.__d=!1),u.__e=t.__e,u.__k=t.__k,u.__k.forEach(function(n){n&&(n.__=u)}),w=0;w<a._sb.length;w++)a.__h.push(a._sb[w]);a._sb=[],a.__h.length&&e.push(a);break n}null!=a.componentWillUpdate&&a.componentWillUpdate(k,a.__s,g),null!=a.componentDidUpdate&&a.__h.push(function(){a.componentDidUpdate(v,d,p)})}if(a.context=g,a.props=k,a.__P=n,C=l.__r,x=0,"prototype"in T&&T.prototype.render){for(a.state=a.__s,a.__d=!1,C&&C(u),s=a.render(a.props,a.state,a.context),P=0;P<a._sb.length;P++)a.__h.push(a._sb[P]);a._sb=[]}else do{a.__d=!1,C&&C(u),s=a.render(a.props,a.state,a.context),a.state=a.__s}while(a.__d&&++x<25);a.state=a.__s,null!=a.getChildContext&&(i=y(y({},i),a.getChildContext())),h||null==a.getSnapshotBeforeUpdate||(p=a.getSnapshotBeforeUpdate(v,d)),E=null!=s&&s.type===_&&null==s.key?s.props.children:s,$(n,Array.isArray(E)?E:[E],u,t,i,r,o,e,f,c),a.base=u.__e,u.__h=null,a.__h.length&&e.push(a),m&&(a.__E=a.__=null),a.__e=!1}else null==o&&u.__v===t.__v?(u.__k=t.__k,u.__e=t.__e):u.__e=L(t.__e,u,t,i,r,o,e,c);(s=l.diffed)&&s(u)}catch(n){u.__v=null,(c||null!=o)&&(u.__e=f,u.__h=!!c,o[o.indexOf(f)]=null),l.__e(n,u,t)}}function F(n,u){l.__c&&l.__c(u,n),n.some(function(u){try{n=u.__h,u.__h=[],n.some(function(n){n.call(u)})}catch(n){l.__e(n,u.__v)}})}function L(l,u,t,i,r,o,e,f){var c,a,h,y=t.props,d=u.props,p=u.type,_=0;if("svg"===p&&(r=!0),null!=o)for(;_<o.length;_++)if((c=o[_])&&"setAttribute"in c==!!p&&(p?c.localName===p:3===c.nodeType)){l=c,o[_]=null;break}if(null==l){if(null===p)return document.createTextNode(d);l=r?document.createElementNS("http://www.w3.org/2000/svg",p):document.createElement(p,d.is&&d),o=null,f=!1}if(null===p)y===d||f&&l.data===d||(l.data=d);else{if(o=o&&n.call(l.childNodes),a=(y=t.props||s).dangerouslySetInnerHTML,h=d.dangerouslySetInnerHTML,!f){if(null!=o)for(y={},_=0;_<l.attributes.length;_++)y[l.attributes[_].name]=l.attributes[_].value;(h||a)&&(h&&(a&&h.__html==a.__html||h.__html===l.innerHTML)||(l.innerHTML=h&&h.__html||""))}if(m(l,d,y,r,f),h)u.__k=[];else if(_=u.props.children,$(l,Array.isArray(_)?_:[_],u,t,i,r&&"foreignObject"!==p,o,e,o?o[0]:t.__k&&C(t,0),f),null!=o)for(_=o.length;_--;)null!=o[_]&&v(o[_]);f||("value"in d&&void 0!==(_=d.value)&&(_!==l.value||"progress"===p&&!_||"option"===p&&_!==y.value)&&b(l,"value",_,y.value,!1),"checked"in d&&void 0!==(_=d.checked)&&_!==l.checked&&b(l,"checked",_,y.checked,!1))}return l}function M(n,u,t){try{"function"==typeof n?n(u):n.current=u}catch(n){l.__e(n,t)}}function N(n,u,t){var i,r;if(l.unmount&&l.unmount(n),(i=n.ref)&&(i.current&&i.current!==n.__e||M(i,null,u)),null!=(i=n.__c)){if(i.componentWillUnmount)try{i.componentWillUnmount()}catch(n){l.__e(n,u)}i.base=i.__P=null,n.__c=void 0}if(i=n.__k)for(r=0;r<i.length;r++)i[r]&&N(i[r],u,t||"function"!=typeof n.type);t||null==n.__e||v(n.__e),n.__=n.__e=n.__d=void 0}function O(n,l,u){return this.constructor(n,u)}function R(u,t,i){var r,o,e;l.__&&l.__(u,t),o=(r="function"==typeof i)?null:i&&i.__k||t.__k,e=[],z(t,u=(!r&&i||t).__k=d(_,null,[u]),o||s,s,void 0!==t.ownerSVGElement,!r&&i?[i]:o?null:t.firstChild?n.call(t.childNodes):null,e,!r&&i?i:o?o.__e:t.firstChild,r),F(e,u)}n=a.slice,l={__e:function(n,l,u,t){for(var i,r,o;l=l.__;)if((i=l.__c)&&!i.__)try{if((r=i.constructor)&&null!=r.getDerivedStateFromError&&(i.setState(r.getDerivedStateFromError(n)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(n,t||{}),o=i.__d),o)return i.__E=i}catch(l){n=l}throw n}},u=0,t=function(n){return null!=n&&void 0===n.constructor},i=!1,A.prototype.setState=function(n,l){var u;u=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=y({},this.state),"function"==typeof n&&(n=n(y({},u),this.props)),n&&y(u,n),null!=n&&this.__v&&(l&&this._sb.push(l),E(this))},A.prototype.forceUpdate=function(n){this.__v&&(this.__e=!0,n&&this.__h.push(n),E(this))},A.prototype.render=_,r=[],e="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,T.__r=0,f=0,c={__proto__:null,render:R,hydrate:function n(l,u){R(l,u,n)},createElement:d,h:d,Fragment:_,createRef:function(){return{current:null}},isValidElement:t,Component:A,cloneElement:function(l,u,t){var i,r,o,e=y({},l.props);for(o in u)"key"==o?i=u[o]:"ref"==o?r=u[o]:e[o]=u[o];return arguments.length>2&&(e.children=arguments.length>3?n.call(arguments,2):t),p(l.type,e,i||l.key,r||l.ref,null)},createContext:function(n,l){var u={__c:l="__cC"+f++,__:n,Consumer:function(n,l){return n.children(l)},Provider:function(n){var u,t;return this.getChildContext||(u=[],(t={})[l]=this,this.getChildContext=function(){return t},this.shouldComponentUpdate=function(n){this.props.value!==n.value&&u.some(function(n){n.__e=!0,E(n)})},this.sub=function(n){u.push(n);var l=n.componentWillUnmount;n.componentWillUnmount=function(){u.splice(u.indexOf(n),1),l&&l.call(n)}}),n.children}};return u.Provider.__=u.Consumer.contextType=u},toChildArray:function n(l,u){return u=u||[],null==l||"boolean"==typeof l||(Array.isArray(l)?l.some(function(l){n(l,u)}):u.push(l)),u},options:l},typeof module<"u"?module.exports=c:self.preact=c;
//# sourceMappingURL=preact.min.module.js.map
