import * as React from 'react';
import styles from './LeaveHistory.module.scss';
import type { ILeaveHistoryProps } from './ILeaveHistoryProps';
import {
  Stack,
  Text,
  DetailsList,
  DetailsListLayoutMode,
  IColumn,
  SelectionMode,
  CommandBar,
  ICommandBarItemProps,
  MessageBar,
  MessageBarType,
  Spinner,
  SpinnerSize,
  Panel,
  PanelType,
  DefaultButton,
  PrimaryButton,
  Dialog,
  DialogType,
  DialogFooter,
  TextField,
  Dropdown,
  IDropdownOption,
  DatePicker,
  Checkbox,
  Label,
  Pivot,
  PivotItem,
  Card,
  ICardTokens
} from '@fluentui/react';
import { ServiceManager } from '../../../services';
import {
  ILeaveRequest,
  ILeaveBalance,
  ILeaveType,
  ApprovalStatus,
  LeaveTypeUtils,
  CommonUtils
} from '../../../models';

interface ILeaveHistoryState {
  leaveRequests: ILeaveRequest[];
  leaveBalances: ILeaveBalance[];
  leaveTypes: ILeaveType[];
  isLoading: boolean;
  error: string;
  selectedRequest: ILeaveRequest | undefined;
  isPanelOpen: boolean;
  isEditDialogOpen: boolean;
  isCancelDialogOpen: boolean;
  editFormData: any;
  selectedPivotKey: string;
}

export default class LeaveHistory extends React.Component<ILeaveHistoryProps, ILeaveHistoryState> {
  private serviceManager: ServiceManager;

  constructor(props: ILeaveHistoryProps) {
    super(props);

    this.serviceManager = new ServiceManager(props.context);

    this.state = {
      leaveRequests: [],
      leaveBalances: [],
      leaveTypes: [],
      isLoading: true,
      error: '',
      selectedRequest: undefined,
      isPanelOpen: false,
      isEditDialogOpen: false,
      isCancelDialogOpen: false,
      editFormData: {},
      selectedPivotKey: 'requests'
    };
  }

  public async componentDidMount(): Promise<void> {
    await this.loadData();
  }

  private async loadData(): Promise<void> {
    try {
      this.setState({ isLoading: true, error: '' });

      const dashboardData = await this.serviceManager.getUserDashboardData();

      this.setState({
        leaveRequests: dashboardData.leaveRequests,
        leaveBalances: dashboardData.leaveBalances,
        leaveTypes: dashboardData.leaveTypes,
        isLoading: false
      });
    } catch (error) {
      console.error('Error loading leave history data:', error);
      this.setState({
        error: 'Failed to load leave history. Please refresh the page.',
        isLoading: false
      });
    }
  }

  private getColumns(): IColumn[] {
    return [
      {
        key: 'leaveType',
        name: 'Leave Type',
        fieldName: 'leaveType',
        minWidth: 120,
        maxWidth: 150,
        isResizable: true,
        onRender: (item: ILeaveRequest) => (
          <span style={{
            color: this.getLeaveTypeColor(item.LeaveType.Id),
            fontWeight: 600
          }}>
            {item.LeaveType.Title}
          </span>
        )
      },
      {
        key: 'startDate',
        name: 'Start Date',
        fieldName: 'startDate',
        minWidth: 100,
        maxWidth: 120,
        isResizable: true,
        onRender: (item: ILeaveRequest) => item.StartDate.toLocaleDateString()
      },
      {
        key: 'endDate',
        name: 'End Date',
        fieldName: 'endDate',
        minWidth: 100,
        maxWidth: 120,
        isResizable: true,
        onRender: (item: ILeaveRequest) => item.EndDate.toLocaleDateString()
      },
      {
        key: 'totalDays',
        name: 'Days',
        fieldName: 'totalDays',
        minWidth: 60,
        maxWidth: 80,
        isResizable: true,
        onRender: (item: ILeaveRequest) => (
          <span>
            {item.TotalDays || CommonUtils.calculateBusinessDays(item.StartDate, item.EndDate)}
            {item.IsPartialDay && ` (${item.PartialDayHours}h)`}
          </span>
        )
      },
      {
        key: 'status',
        name: 'Status',
        fieldName: 'status',
        minWidth: 100,
        maxWidth: 120,
        isResizable: true,
        onRender: (item: ILeaveRequest) => (
          <span className={`${styles.statusBadge} ${styles[item.ApprovalStatus.toLowerCase()]}`}>
            {item.ApprovalStatus}
          </span>
        )
      },
      {
        key: 'submissionDate',
        name: 'Submitted',
        fieldName: 'submissionDate',
        minWidth: 100,
        maxWidth: 120,
        isResizable: true,
        onRender: (item: ILeaveRequest) => item.SubmissionDate.toLocaleDateString()
      },
      {
        key: 'actions',
        name: 'Actions',
        fieldName: 'actions',
        minWidth: 100,
        maxWidth: 150,
        isResizable: false,
        onRender: (item: ILeaveRequest) => (
          <Stack horizontal tokens={{ childrenGap: 8 }}>
            <DefaultButton
              text="View"
              iconProps={{ iconName: 'View' }}
              onClick={() => this.onViewRequest(item)}
              styles={{ root: { minWidth: 'auto' } }}
            />
            {item.ApprovalStatus === ApprovalStatus.Pending && (
              <>
                <DefaultButton
                  text="Edit"
                  iconProps={{ iconName: 'Edit' }}
                  onClick={() => this.onEditRequest(item)}
                  styles={{ root: { minWidth: 'auto' } }}
                />
                <DefaultButton
                  text="Cancel"
                  iconProps={{ iconName: 'Cancel' }}
                  onClick={() => this.onCancelRequest(item)}
                  styles={{ root: { minWidth: 'auto' } }}
                />
              </>
            )}
          </Stack>
        )
      }
    ];
  }

  private getLeaveTypeColor(leaveTypeId: number): string {
    const leaveType = this.state.leaveTypes.filter((lt: ILeaveType) => lt.Id === leaveTypeId)[0];
    return leaveType?.ColorCode || '#0078d4';
  }

  private onViewRequest = (request: ILeaveRequest): void => {
    this.setState({
      selectedRequest: request,
      isPanelOpen: true
    });
  };

  private onEditRequest = (request: ILeaveRequest): void => {
    this.setState({
      selectedRequest: request,
      editFormData: {
        leaveTypeId: request.LeaveType.Id,
        startDate: request.StartDate,
        endDate: request.EndDate,
        isPartialDay: request.IsPartialDay,
        partialDayHours: request.PartialDayHours,
        comments: request.RequestComments || '',
        attachmentUrl: request.AttachmentURL || ''
      },
      isEditDialogOpen: true
    });
  };

  private onCancelRequest = (request: ILeaveRequest): void => {
    this.setState({
      selectedRequest: request,
      isCancelDialogOpen: true
    });
  };

  private onClosePanel = (): void => {
    this.setState({
      isPanelOpen: false,
      selectedRequest: undefined
    });
  };

  private onCloseEditDialog = (): void => {
    this.setState({
      isEditDialogOpen: false,
      selectedRequest: undefined,
      editFormData: {}
    });
  };

  private onCloseCancelDialog = (): void => {
    this.setState({
      isCancelDialogOpen: false,
      selectedRequest: undefined
    });
  };
